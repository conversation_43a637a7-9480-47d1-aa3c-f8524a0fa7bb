@charset "UTF-8";

.zhi-block {

  position: relative;

  overflow: hidden;

  clear: both; }

  .zhi-block .zhi-block-inner {

    clear: both;

    overflow: hidden; }

  .zhi-block .zhi-block-row {

    overflow: hidden; }



/* --------------------- Pagination and Load More Styling -------------------- */

.zhi-block .zhi-pagination {

  display: -webkit-box;

  display: -ms-flexbox;

  display: flex;

  -webkit-box-orient: horizontal;

  -webkit-box-direction: normal;

      -ms-flex-flow: row wrap;

          flex-flow: row wrap;

  -webkit-box-pack: center;

      -ms-flex-pack: center;

          justify-content: center;

  margin-top: 50px;

  clear: both; }

  .zhi-block .zhi-pagination .zhi-page-nav {

    -webkit-box-flex: 0;

    box-flex: 0;

    -moz-flex: 0 1 auto;

    -ms-flex: 0 1 auto;

    flex: 0 1 auto;

    font-size: 15px;

    line-height: 24px;

    color: #666;

    padding: 5px 15px;

    margin: 0 2px;

    border-right: 1px solid #dcdcdc;

    border-top: 1px solid #dcdcdc;

    border-bottom: 1px solid #dcdcdc;

    border-left: 1px solid #dcdcdc;

    margin-bottom: 10px;

    outline: none;

    cursor: pointer;

    border-radius: 2px;

    -webkit-transition: all 0.3s ease-in-out 0s;

    transition: all 0.3s ease-in-out 0s; }

    .zhi-block .zhi-pagination .zhi-page-nav i {

      color: #222;

      font-size: 12px;

      line-height: 1; }

      .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav i {

        color: #bbb; }

    .zhi-block .zhi-pagination .zhi-page-nav[data-page="prev"], .zhi-block .zhi-pagination .zhi-page-nav[data-page="next"] {

      padding: 5px 18px; }

    .zhi-block .zhi-pagination .zhi-page-nav[data-page="next"] {

      margin-right: 0; }

    .zhi-block .zhi-pagination .zhi-page-nav.zhi-dotted {

      border: none;

      pointer-events: none;

      padding: 5px 8px; }

    .zhi-block .zhi-pagination .zhi-page-nav:hover, .zhi-block .zhi-pagination .zhi-page-nav.zhi-current-page {

      background: #eee; }

    .zhi-block .zhi-pagination .zhi-page-nav.zhi-disabled, .zhi-block .zhi-pagination .zhi-page-nav.zhi-current-page {

      pointer-events: none; }

    .zhi-block .zhi-pagination .zhi-page-nav.zhi-disabled, .zhi-block .zhi-pagination .zhi-page-nav.zhi-disabled i {

      color: #999;

      border-color: #eaeaea; }

      .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav.zhi-disabled, .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav.zhi-disabled i {

        border-color: #444;

        color: #888; }

    .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav {

      border-color: #606060;

      color: #aaa; }

      .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav:hover, .zhi-dark-bg .zhi-block .zhi-pagination .zhi-page-nav.zhi-current-page {

        background: #333; }

.zhi-block .zhi-load-more {

  color: #fff;

  padding: 15px 25px;

  border-radius: 999px;

  outline: none;

  -webkit-transition: all 0.3s ease-in-out 0s;

  transition: all 0.3s ease-in-out 0s; }

  .zhi-block .zhi-load-more:hover {

    color: #fff;

    background: #333;

    border-color: #333; }

  .zhi-block .zhi-load-more.zhi-disabled {

    display: none; }

.zhi-block .zhi-loading {

  position: relative;

  margin: 0 -51px 0 15px;

  width: 36px;

  height: 36px;

  background: url(../assets/loading.gif) center center no-repeat;

  visibility: hidden; }

.zhi-block .zhi-loader-gif {

  position: absolute;

  left: 0;

  top: 0;

  overflow: hidden;

  width: 100%;

  height: 100%;

  background: transparent url(../assets/bricks-loader.gif) center center no-repeat;

  display: none; }

.zhi-block.zhi-fetching .zhi-block-inner {

  opacity: 0.3; }

.zhi-block.zhi-fetching .zhi-loader-gif {

  display: block; }

.zhi-block.zhi-processing .zhi-loader-gif {

  display: block; }

.zhi-block.zhi-processing .zhi-load-more-nav .zhi-loading {

  visibility: visible; }



/* ------------------- General Block Header Styling ---------------------------- */

.zhi-block .zhi-block-header {  

  margin-left: auto;

  margin-right: auto;

  clear: both;

  position: relative;

  z-index: 5; }

.zhi-block .zhi-heading {

  display: block;

  padding: 0 0 15px 0;

  margin: 0 0 30px 0;

  text-align: left;

  max-width: none;

  font-size: 0;

  line-height: 0; }

.zhi-block .zhi-heading span, .zhi-block .zhi-heading a {

  font-size: 18px;

  line-height: 28px;

  text-transform: uppercase;

  letter-spacing: 1px;

  color: #333;

  text-align: left;

  display: inline-block;

  margin: 0 100px 0 0; }

  .zhi-dark-bg .zhi-block .zhi-heading span, .zhi-dark-bg .zhi-block .zhi-heading a {

    color: #e5e5e5; }

.zhi-block .zhi-heading a {

  -webkit-transition: color 0.3s ease-in-out 0s;

  transition: color 0.3s ease-in-out 0s; }

  .zhi-block .zhi-heading a:hover {

    color: #666; }

.zhi-block .zhi-block-filter {

  position: absolute;

  top: 0;

  right: 0;

  margin: 0;

  z-index: 2;

  text-align: right; }

  .zhi-dark-bg .zhi-block .zhi-block-filter {

    border-color: #666; }

  .zhi-block .zhi-block-filter .zhi-block-filter-item {

    display: inline-block; }

    @media only screen and (max-width: 479px) {

      .zhi-block .zhi-block-filter .zhi-block-filter-item {

        margin-right: 8px; } }

    .zhi-block .zhi-block-filter .zhi-block-filter-item a {

      padding: 0 15px 8px;

      -webkit-transition: all 0.4s ease-in-out 0s;

      transition: all 0.4s ease-in-out 0s;

      display: block; }

      @media only screen and (max-width: 479px) {

        .zhi-block .zhi-block-filter .zhi-block-filter-item a {

          padding: 0 10px 8px; } }

    .zhi-block .zhi-block-filter .zhi-block-filter-item.zhi-active {

      color: #f94213; }

    .zhi-block .zhi-block-filter .zhi-block-filter-item:last-child {

      margin-right: 0; }

  .zhi-block .zhi-block-filter .zhi-block-filter-dropdown {

    display: inline-block;

    text-align: right;

    position: relative; }

  .zhi-block .zhi-block-filter ul.zhi-block-filter-list {

    display: inline-block;

    line-height: 1; }

  .zhi-block .zhi-block-filter .zhi-block-filter-more {

    cursor: pointer;

    padding-left: 15px;

    -moz-user-select: none;

    -webkit-user-select: none;

    -ms-user-select: none;

    display: inline-block; }

    .zhi-block .zhi-block-filter .zhi-block-filter-more i.zhi-icon-arrow-right3 {

      display: inline-block;

      font-size: 10px;

      line-height: 1;

      vertical-align: middle;

      -webkit-transform: rotate(45deg);

      transform: rotate(90deg);

      margin: 0 6px;

      color: #000; }

  .zhi-block .zhi-block-filter .zhi-block-filter-dropdown:hover .zhi-block-filter-more {

    background-color: #222; }

    .zhi-block .zhi-block-filter .zhi-block-filter-dropdown:hover .zhi-block-filter-more span, .zhi-block .zhi-block-filter .zhi-block-filter-dropdown:hover .zhi-block-filter-more i {

      color: #fff; }

  .zhi-block .zhi-block-filter ul.zhi-block-filter-dropdown-list {

    list-style: none;

    position: absolute;

    right: 0;

    top: 100%;

    padding: 6px 0;

    background-color: #ffffff;

    background-color: rgba(255, 255, 255, 0.95);

    z-index: 999;

    border: 1px solid #ededed;

    display: none; }

    .zhi-block .zhi-block-filter ul.zhi-block-filter-dropdown-list:hover i.zhi-icon-arrow-right3 {

      color: #fff; }

  .zhi-block .zhi-block-filter .zhi-block-filter-dropdown:hover ul.zhi-block-filter-dropdown-list {

    display: block; }

  .zhi-block .zhi-block-filter ul.zhi-block-filter-dropdown-list li {

    margin-left: 0; }

    .zhi-block .zhi-block-filter ul.zhi-block-filter-dropdown-list li a {

      white-space: nowrap;

      display: block;

      padding: 8px 15px 8px 25px; }

  .zhi-block .zhi-block-filter .zhi-block-filter-item a, .zhi-block .zhi-block-filter .zhi-block-filter-more span, .zhi-block .zhi-block-filter ul.zhi-block-filter-dropdown-list li a {

    color: #666;

    font-size: 12px;

    line-height: 18px;

    text-transform: uppercase; }

  .zhi-block .zhi-block-filter .zhi-block-filter-item a:hover, .zhi-block .zhi-block-filter .zhi-block-filter-item.zhi-active a {

    color: #f94213; }



.zhi-block-header-expanded .zhi-block-header {

  position: relative;



  margin: 0 auto 30px;

  overflow: hidden;

  clear: both;

  display: -webkit-box;

  display: -ms-flexbox;

  display: flex;

  -webkit-box-orient: horizontal;

  -webkit-box-direction: normal;

      -ms-flex-flow: row nowrap;

          flex-flow: row nowrap;

  -webkit-box-pack: justify;

      -ms-flex-pack: justify;

          justify-content: space-between; }

  .zhi-block-header-expanded .zhi-block-header.zhi-no-heading {

    -webkit-box-pack: center;

        -ms-flex-pack: center;

            justify-content: center; }

  @media only screen and (max-width: 800px) {

    .zhi-block-header-expanded .zhi-block-header {

      -webkit-box-orient: vertical;

      -webkit-box-direction: normal;

          -ms-flex-flow: column wrap;

              flex-flow: column wrap;

      -webkit-box-pack: start;

          -ms-flex-pack: start;

              justify-content: flex-start; } }

.zhi-block-header-expanded .zhi-heading {

  padding: 0;

  margin: 0; }

  .zhi-dark-bg .zhi-block-header-expanded .zhi-heading {

    color: #e5e5e5; }

  @media only screen and (max-width: 800px) {

    .zhi-block-header-expanded .zhi-heading {

      margin-bottom: 30px; } }

.zhi-block-header-expanded .zhi-heading span, .zhi-block-header-expanded .zhi-heading a {

  font-size: 32px;

  line-height: 44px; }

.zhi-block-header-expanded .zhi-taxonomy-filter {

  display: block;

  margin: 0;

  padding: 0;

  -webkit-align-self: center;

  align-self: center;

  -ms-flex-item-align: center; }

  @media only screen and (max-width: 800px) {

    .zhi-block-header-expanded .zhi-taxonomy-filter {

      -webkit-align-self: flex-start;

      align-self: flex-start;

      -ms-flex-item-align: start; } }

  .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item {

    position: relative;

    display: inline-block;

    margin: 0 0 15px 0;

    padding: 0;

    font-style: normal; }

    .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item a {

      font-size: 15px;

      line-height: 24px;

      padding: 0 15px;

      -webkit-transition: all 0.4s ease-in-out 0s;

      transition: all 0.4s ease-in-out 0s;

      display: block;

      color: #777; }

      .zhi-dark-bg .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item a {

        color: #999; }

      .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item a:hover {

        color: #222; }

        .zhi-dark-bg .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item a:hover {

          color: #fff; }

      @media only screen and (max-width: 479px) {

        .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item a {

          padding: 0 10px; } }

    .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item:first-child a {

      padding-left: 0; }

    .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

      color: #222; }

      .zhi-dark-bg .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

        color: #fff; }

    .zhi-block-header-expanded .zhi-taxonomy-filter .zhi-filter-item:last-child {

      margin-right: 0; }



/* --------- Block Header 1 --------- */

.zhi-block-header-1 .zhi-heading {

  border-bottom: 2px solid #ddd; }

  .zhi-block-header-1 .zhi-heading:after {

    content: "";

    background: #f94213;

    width: 50px;

    height: 2px;

    position: absolute;

    bottom: -1px;

    left: 0; }

.zhi-block-header-1 .zhi-no-heading .zhi-heading {

  min-height: 40px; }

  .zhi-block-header-1 .zhi-no-heading .zhi-heading:after {

    display: none; }



/* --------- Block Header 2 --------- */

.zhi-block-header-2 .zhi-heading:before, .zhi-block-header-2 .zhi-heading:after {

  content: '';

  width: 100%;

  height: 1px;

  position: absolute;

  left: 0;

  background-color: #ddd; }

.zhi-block-header-2 .zhi-heading:before {

  bottom: 4px; }

.zhi-block-header-2 .zhi-heading:after {

  bottom: 0; }

.zhi-block-header-2 .zhi-no-heading .zhi-heading {

  min-height: 40px; }



/* --------- Block Header 3 --------- */

.zhi-block-header-3 .zhi-heading {

  padding: 0;

  border-bottom: 1px solid #ddd; }

  .zhi-block-header-3 .zhi-heading a, .zhi-block-header-3 .zhi-heading span {

    position: relative;

    padding: 4px 8px 4px 8px;

    border-radius: 2px 2px 2px 0;

    background: #333;

    color: #fff; }

    .zhi-block-header-3 .zhi-heading a:after, .zhi-block-header-3 .zhi-heading span:after {

      content: '';

      display: block;

      position: absolute;

      width: 0;

      height: 0;

      position: absolute;

      bottom: -8px;

      left: 0;

      border-left: 0;

      border-right: 8px solid transparent;

      border-top: 8px solid #333; }

  .zhi-block-header-3 .zhi-heading a:hover {

    color: #ddd; }

.zhi-block-header-3 .zhi-heading span, .zhi-block-header-3 .zhi-heading a {

  font-size: 15px;

  line-height: 24px; }

.zhi-block-header-3 .zhi-no-heading .zhi-heading {

  min-height: 40px; }



/* --------- Block Header 4 --------- */

.zhi-block-header-4 .zhi-heading {

  padding: 0;

  border: 1px solid #333;

  border-bottom: 2px solid #333;

  text-align: center; }

  .zhi-block-header-4 .zhi-heading a, .zhi-block-header-4 .zhi-heading span {

    position: relative;

    padding: 8px 10px;

    border-radius: 0;

    margin: 0 auto; }

    .zhi-block-header-4 .zhi-heading a:after, .zhi-block-header-4 .zhi-heading span:after {

      content: '';

      display: block;

      position: absolute;

      bottom: 0;

      left: 0;

      right: 0;

      width: 8px;

      bottom: -8px;

      margin: 0 auto;

      border-left: 8px solid transparent;

      border-top: 8px solid #43494a;

      border-right: 8px solid transparent; }

.zhi-block-header-4 .zhi-heading span, .zhi-block-header-4 .zhi-heading a {

  font-size: 15px;

  line-height: 24px; }

.zhi-block-header-4 .zhi-block-header .zhi-block-filter {

  padding: 8px 10px; }

.zhi-block-header-4 .zhi-no-heading .zhi-heading {

  min-height: 45px; }



/* --------- Block Header 5 --------- */

.zhi-block-header-5 .zhi-heading {

  background: #fbfbfb;

  border-bottom: 2px solid #eee;

  border-top: 1px solid #eee;

  padding: 10px 15px; }

.zhi-block-header-5 .zhi-block-header .zhi-block-filter {

  padding: 10px 8px; }

.zhi-block-header-5 .zhi-no-heading .zhi-heading {

  min-height: 50px; }



/* ---------- Block Header 6 ---------- */

.zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item {

  border-bottom: 1px solid #ddd; }

  .zhi-dark-bg .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item {

    border-color: #444; }

  .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item a {

    padding: 0 15px 15px; }

    @media only screen and (max-width: 479px) {

      .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item a {

        padding: 0 10px 8px; } }

  .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item:first-child a {

    padding-left: 15px; }

  .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

    color: #222; }

    .zhi-dark-bg .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

      color: #fff; }

  .zhi-block-header-6 .zhi-taxonomy-filter .zhi-filter-item.zhi-active:after {

    content: '';

    position: absolute;

    left: 0;

    bottom: 0;

    border-bottom: 3px solid #f94213;

    width: 100%; }



/* ---------- Block Header 6 ---------- */

.zhi-block-header-7 .zhi-heading span, .zhi-block-header-7 .zhi-heading a {

  font-size: 26px;

  line-height: 34px; }

.zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item a {

  padding: 0 20px 0 18px;

  font-style: italic; }

.zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item:after {

  content: '/';

  position: absolute;

  right: 0;

  top: 0;

  color: #bbb;

  font-size: 14px; }

.zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item:last-child:after {

  content: ''; }

.zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item:hover a, .zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

  color: #f94213; }

  .zhi-dark-bg .zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item:hover a, .zhi-dark-bg .zhi-block-header-7 .zhi-taxonomy-filter .zhi-filter-item.zhi-active a {

    color: #fff; }



/* ------------------------ General Module Styling ------------------------------ */

.zhi-module {

  display: block;

  position: relative;

  border: none;

  background: none;

  -webkit-box-shadow: none;

          box-shadow: none;

  padding-bottom: 30px; }

  @media only screen and (max-width: 1024px) {

    .zhi-module {

      padding-bottom: 20px; } }

  .zhi-module .zhi-module-image {

    position: relative; }

    .zhi-module .zhi-module-image .zhi-module-image-info {

      opacity: 0;

      -webkit-transition: opacity 0.4s ease-in-out 0s;

      transition: opacity 0.4s ease-in-out 0s; }

      .zhi-module .zhi-module-image .zhi-module-image-info .zhi-module-entry-info {

        text-align: center;

        display: block;

        position: absolute;

        top: 50%;

        left: 0;

        right: 0;

        margin: auto;

        max-width: 100%;

        -webkit-transform: translateY(-50%);

                transform: translateY(-50%); }

      .zhi-module .zhi-module-image .zhi-module-image-info .zhi-post-title, .zhi-module .zhi-module-image .zhi-module-image-info .entry-title {

        padding: 10px;

        margin: 0;

        font-size: 18px;

        line-height: 28px;

        font-weight: 400;

        color: #fff; }

        @media only screen and (max-width: 1024px) {

          .zhi-module .zhi-module-image .zhi-module-image-info .zhi-post-title, .zhi-module .zhi-module-image .zhi-module-image-info .entry-title {

            font-size: 18px;

            line-height: 26px; } }

        .zhi-module .zhi-module-image .zhi-module-image-info .zhi-post-title a, .zhi-module .zhi-module-image .zhi-module-image-info .entry-title a {

          display: inline;

          color: #fff;

          -webkit-transition: all 0.3s ease-in-out 0s;

          transition: all 0.3s ease-in-out 0s;

          border-bottom: 1px solid transparent; }

          .zhi-module .zhi-module-image .zhi-module-image-info .zhi-post-title a:hover, .zhi-module .zhi-module-image .zhi-module-image-info .entry-title a:hover {

            border-bottom: 1px solid #ccc; }

    .zhi-module .zhi-module-image:hover .zhi-module-image-info {

      opacity: 1; }

    .zhi-module .zhi-module-image:hover .zhi-lightbox-item {

      display: block;

      background: rgba(0, 0, 0, 0.4); }

  .zhi-module .zhi-module-thumb {

    position: relative;

    overflow: hidden;

    margin: 0 0 15px 0; }

    .zhi-module .zhi-module-thumb img {

      display: block;

      width: 100%;

      -webkit-transition: all 0.4s ease-in-out 0s;

      transition: all 0.4s ease-in-out 0s; }

      .zhi-module .zhi-module-thumb img:hover {

        -webkit-filter: brightness(80%);

                filter: brightness(80%); }

    .zhi-module .zhi-module-thumb .zhi-lightbox-item {

      display: none;

      position: absolute;

      top: 0;

      right: 0;

      line-height: 1;

      padding: 12px 15px;

      background: transparent;

      -webkit-transition: all 0.3s ease-in-out 0s;

      transition: all 0.3s ease-in-out 0s; }

      .zhi-module .zhi-module-thumb .zhi-lightbox-item:hover {

        background: rgba(0, 0, 0, 0.6); }

      .zhi-module .zhi-module-thumb .zhi-lightbox-item i {

        color: #fff;

        font-size: 18px; }

    .zhi-module .zhi-module-thumb:hover {

      background: rgba(0, 0, 0, 0.3); }

      .zhi-module .zhi-module-thumb:hover .zhi-lightbox-item {

        display: block;

        background: rgba(0, 0, 0, 0.4); }

  .zhi-module .zhi-module-entry-text {

    text-align: center;

    max-width: 650px;

    margin: 20px auto 0; }

  .zhi-module .entry-title {

    font-size: 20px;

    line-height: 28px;

    font-weight: normal;

    margin: 0 0 10px 0; }

    .zhi-module .entry-title:after, .zhi-module .entry-title:before {

      display: none; }

    .zhi-module .entry-title a {

      -webkit-transition: all 0.4s ease-in-out 0s;

      transition: all 0.4s ease-in-out 0s;

      color: #333; }

      .zhi-module .entry-title a:hover {

        color: #666; }

    .zhi-dark-bg .zhi-module .entry-title a {

      color: #e0e0e0; }

      .zhi-dark-bg .zhi-module .entry-title a:hover {

        color: #fff; }

  .zhi-module .zhi-module-meta {

    font-size: 12px;

    line-height: 18px;

    margin: 0 0 8px 0; }

    .zhi-module .zhi-module-meta span {

      display: inline-block;

      padding: 0;

      margin: 0;

      color: #999; }

      .zhi-module .zhi-module-meta span:after {

        content: '/';

        padding-left: 6px;

        padding-right: 2px; }

      .zhi-module .zhi-module-meta span:first-child {

        border: none;

        padding-left: 0; }

      .zhi-module .zhi-module-meta span:last-child:after {

        display: none; }

      .zhi-module .zhi-module-meta span a {

        -webkit-transition: all 0.3s ease-in-out 0s;

        transition: all 0.3s ease-in-out 0s;

        font-style: normal;

        color: #444; }

        .zhi-dark-bg .zhi-module .zhi-module-meta span a {

          color: #999; }

        .zhi-module .zhi-module-meta span a:hover {

          color: #888; }

          .zhi-dark-bg .zhi-module .zhi-module-meta span a:hover {

            color: #bbb; }

      .zhi-module .zhi-module-meta span abbr {

        text-decoration: initial; }

      .zhi-dark-bg .zhi-module .zhi-module-meta span {

        color: #707070; }

  .zhi-module .zhi-read-more a {

    padding: 10px 16px;

    text-transform: none;

    letter-spacing: 0; }

    @media only screen and (min-width: 1024px) {

      .zhi-module .zhi-read-more a {

        padding: 12px 18px; } }

  .zhi-module .entry-summary {

    margin: 15px auto 0;

    padding: 0; }

    .zhi-dark-bg .zhi-module .entry-summary {

      color: #999; }



/* ---------- Module 1 ----------- */

.zhi-module-1 .zhi-module-image .zhi-terms {

  display: block;

  position: absolute;

  bottom: 0;

  font-size: 12px;

  line-height: 1;

  background: rgba(0, 0, 0, 0.6);

  color: #fff;

  margin-right: 5px;

  padding: 8px 12px; }

  .zhi-module-1 .zhi-module-image .zhi-terms a {

    display: inline-block;

    color: #fff; }



/* ------------ Module 3 ---------------- */

.zhi-module-3 {

  padding-bottom: 26px; }



.zhi-module-3 .zhi-module-thumb {

  position: absolute;

  left: 0;

  top: 0;

  width: 100px; }



@media (min-width: 768px) and (max-width: 1024px) {

  .zhi-module-3 .zhi-module-thumb {

    width: 80px; } }

.zhi-module-3 .zhi-entry-details {

  margin-left: 116px;

  min-height: 70px; }



@media (min-width: 768px) and (max-width: 1024px) {

  .zhi-module-3 .zhi-entry-details {

    margin-left: 95px;

    min-height: 55px; } }

.zhi-module-3 .entry-title {

  font-size: 14px;

  line-height: 20px;

  margin-bottom: 4px;

  font-weight: 500; }



@media (min-width: 768px) and (max-width: 1170px) {

  .zhi-module-3 .entry-title {

    font-size: 12px;

    line-height: 18px; } }

.zhi-module-3 .zhi-module-meta {

  margin-bottom: 0;

  min-height: 0; }



/* ------------ Module 4 ---------------- */

.zhi-module-4 {

  padding-bottom: 26px; }



.zhi-module-4 .zhi-module-thumb {

  position: absolute;

  right: 0;

  top: 0;

  width: 100px; }



@media (min-width: 768px) and (max-width: 1024px) {

  .zhi-module-4 .zhi-module-thumb {

    width: 80px; } }

.zhi-module-4 .zhi-entry-details {

  margin-right: 108px;

  min-height: 70px; }



@media (min-width: 768px) and (max-width: 1024px) {

  .zhi-module-4 .zhi-entry-details {

    margin-right: 95px;

    min-height: 55px; } }

.zhi-module-4 .entry-title {

  font-size: 14px;

  line-height: 20px;

  margin-bottom: 4px;

  font-weight: 500; }



@media (min-width: 768px) and (max-width: 1170px) {

  .zhi-module-4 .entry-title {

    font-size: 12px;

    line-height: 18px; } }

.zhi-module-4 .zhi-module-meta {

  margin-bottom: 0;

  min-height: 0; }



/* ---------------- Module 5 --------------- */

/*  ---------------- Module 6 --------------- */

.zhi-module-6 {

  padding-bottom: 26px; }



@media (min-width: 767px) {

  .zhi-module-6 {

    padding-bottom: 40px; } }

.zhi-module-6 .zhi-module-thumb {

  position: absolute;

  left: 0;

  top: 0;

  width: 100px; }



@media (min-width: 500px) {

  .zhi-module-6 .zhi-module-thumb {

    width: 150px; } }

@media (min-width: 767px) {

  .zhi-module-6 .zhi-module-thumb {

    width: 220px; } }

.zhi-module-6 .zhi-entry-details {

  margin-left: 115px;

  min-height: 69px; }



@media (min-width: 500px) {

  .zhi-module-6 .zhi-entry-details {

    margin-left: 170px;

    min-height: 103px; } }

@media (min-width: 767px) {

  .zhi-module-6 .zhi-entry-details {

    margin-left: 244px;

    min-height: 150px; } }

.zhi-module-6 .entry-title {

  font-size: 14px;

  line-height: 20px;

  font-weight: 500; }



@media (min-width: 500px) {

  .zhi-module-6 .entry-title {

    font-size: 22px;

    line-height: 28px; } }

.zhi-module-6 .zhi-excerpt {

  display: none; }



@media (min-width: 640px) {

  .zhi-module-6 .zhi-excerpt {

    display: block; } }

/*  ---------------- Module 7 --------------- */

.zhi-module-7 {

  padding-bottom: 30px; }



@media (min-width: 767px) {

  .zhi-module-7 {

    padding-bottom: 50px; } }

.zhi-module-7 .zhi-module-thumb {

  position: absolute;

  left: 0;

  top: 0;

  width: 100px; }



@media (min-width: 500px) {

  .zhi-module-7 .zhi-module-thumb {

    width: 200px; } }

@media (min-width: 767px) {

  .zhi-module-7 .zhi-module-thumb {

    width: 300px; } }

.zhi-module-7 .zhi-entry-details {

  margin-left: 110px;

  min-height: 72px; }



@media (min-width: 500px) {

  .zhi-module-7 .zhi-entry-details {

    margin-left: 220px;

    min-height: 145px; } }

@media (min-width: 767px) {

  .zhi-module-7 .zhi-entry-details {

    margin-left: 330px;

    min-height: 235px; } }

.zhi-module-7 .entry-title {

  font-size: 16px;

  line-height: 24px;

  font-weight: 500; }



@media (min-width: 500px) {

  .zhi-module-7 .entry-title {

    font-size: 20px;

    line-height: 26px; } }

@media (min-width: 1024px) {

  .zhi-module-7 .entry-title {

    font-size: 26px;

    line-height: 34px; } }

.zhi-module-7 .zhi-excerpt {

  display: none; }



@media (min-width: 640px) {

  .zhi-module-7 .zhi-excerpt {

    display: block; } }

@media (min-width: 900px) {

  .zhi-module-7 .zhi-excerpt {

    margin-bottom: 20px; } }

.zhi-module-7 .zhi-read-more {

  display: none; }



@media (min-width: 900px) {

  .zhi-module-7 .zhi-read-more {

    display: block; } }

/* ---------------- Module 8 ---------------------- */

.zhi-module-8 {

  padding-bottom: 30px; }



@media (min-width: 767px) {

  .zhi-module-8 {

    padding-bottom: 40px; } }

@media (min-width: 1024px) {

  .zhi-module-8 {

    padding-bottom: 60px; } }

.zhi-module-8 .zhi-module-thumb .wp-post-image {

  width: 100%; }



.zhi-module-8:last-child {

  padding-bottom: 0; }



.zhi-module-8 .entry-title {

  font-size: 18px;

  line-height: 26px;

  font-weight: 500;

  margin-bottom: 10px; }



@media (min-width: 500px) {

  .zhi-module-8 .entry-title {

    font-size: 22px;

    line-height: 30px; } }

@media (min-width: 767px) {

  .zhi-module-8 .entry-title {

    font-size: 30px;

    line-height: 38px; } }

.zhi-module-8 .zhi-entry-details {

  position: relative;

  padding: 15px 25px;

  margin: -60px auto 0;

  max-width: 90%;

  background: #fff; }



@media (min-width: 767px) {

  .zhi-module-8 .zhi-entry-details {

    padding: 20px 35px;

    margin: -70px auto 0; } }

@media (min-width: 1024px) {

  .zhi-module-8 .zhi-entry-details {

    padding: 25px 35px;

    margin: -85px auto 0; } }

.zhi-module-8 .zhi-excerpt {

  font-size: 14px;

  line-height: 24px;

  margin-top: 11px; }



.zhi-module-8 .zhi-read-more {

  display: none; }



@media (min-width: 500px) {

  .zhi-module-8 .zhi-read-more {

    display: block;

    margin-top: 15px; } }

/* ------------- Module 10 ------------------- */

.zhi-module-10 {

  padding: 30px 0;

  margin: 0;

  text-align: center;

  border-bottom: 1px solid #e8e8e8; }

  .zhi-module-10:last-child {

    border: none; }



@media (min-width: 1024px) {

  .zhi-module-10 {

    padding: 40px 0; } }

.zhi-module-10 .entry-title {

  font-size: 24px;

  line-height: 32px;

  margin: 0 0 10px 0; }



@media (min-width: 1024px) {

  .zhi-module-10 .entry-title {

    font-size: 32px;

    line-height: 44px;

    font-weight: 400;

    margin: 0 0 15px 0; } }

.zhi-module-10 .zhi-entry-details {

  margin-bottom: 20px; }



@media (min-width: 1024px) {

  .zhi-module-10 .zhi-entry-details {

    position: relative;

    width: 100%;

    margin-bottom: 30px; } }

.zhi-module-10 .zhi-terms {

  text-transform: uppercase;

  font-size: 11px;

  line-height: 1;

  background-color: transparent;

  display: block;

  margin-bottom: 20px; }



.zhi-module-10 .zhi-terms a {

  color: #9e9e9e; }



.zhi-module-10 .zhi-module-thumb img {

  width: 100%; }



.zhi-module-10 .zhi-excerpt {

  font-size: 14px;

  line-height: 22px;

  margin-top: 20px; }



@media (min-width: 1024px) {

  .zhi-module-10 .zhi-excerpt {

    font-size: 15px;

    line-height: 26px;

    margin-top: 30px; } }

.zhi-module-10 .zhi-read-more {

  margin: 20px 0 0 0; }



.zhi-module-10 .zhi-read-more a {

  color: #333;

  font-size: 12px;

  line-height: 1;

  font-weight: 600;

  text-transform: uppercase;

  display: inline-block;

  padding: 0;

  -webkit-transition: color 0.3s ease-in-out 0s;

  transition: color 0.3s ease-in-out 0s; }

  .zhi-module-10 .zhi-read-more a:hover {

    color: #666; }

  .zhi-module-10 .zhi-read-more a:after {

    content: '›';

    display: inline-block;

    margin-left: 7px; }



.zhi-module-10 .zhi-read-more a:hover {

  color: #666; }



/* ----------- Module Transparent 1 ------------ */

.zhi-module-trans1 {

  position: relative;

  padding-bottom: 0;

  margin-bottom: 3px; }



.zhi-module-trans1 .zhi-module-thumb {

  margin: 0;

  padding: 0; }

  .zhi-module-trans1 .zhi-module-thumb a.zhi-post-link {

    -webkit-transition: all 0.4s ease-in-out 0s;

    transition: all 0.4s ease-in-out 0s; }

  .zhi-module-trans1 .zhi-module-thumb a.zhi-post-link:before {

    background: -webkit-gradient(linear, left top, left bottom, color-stop(40%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.6)));

    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.6) 100%);

    z-index: 0;

    content: '';

    height: 100%;

    left: 0;

    position: absolute;

    top: 0;

    width: 100%; }

  .zhi-module-trans1 .zhi-module-thumb:hover a.zhi-post-link:before {

    background: -webkit-gradient(linear, left top, left bottom, color-stop(40%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.8)));

    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.8) 100%); }



@media (min-width: 480px) {

  .zhi-module-trans1 .zhi-module-thumb {

    margin: 0 3px 0 0; } }

.zhi-module-trans1 .zhi-entry-details {

  position: absolute;

  bottom: 0;

  padding: 0 20px;

  margin-bottom: 20px;

  z-index: 1; }



.zhi-module-trans1 .zhi-entry-details .zhi-post-author-name a {

  color: #fff; }



.zhi-module-trans1 .entry-title {

  font-size: 18px;

  line-height: 26px; }



@media (min-width: 480px) {

  .zhi-module-trans1 .entry-title {

    font-size: 16px;

    line-height: 22px; } }

@media (min-width: 1024px) {

  .zhi-module-trans1 .entry-title {

    font-size: 21px;

    line-height: 28px; } }

.zhi-module-trans1 .entry-title a, .zhi-module-trans1 .zhi-module-meta span a {

  color: #fff !important; }



.zhi-module-trans1 .entry-title a:hover, .zhi-module-trans1 .zhi-module-meta span a:hover {

  color: #ddd !important; }



.zhi-module-trans1 .zhi-module-meta span:after, .zhi-module-trans1 .zhi-module-meta span {

  color: #bbb !important; }



/* ----------------------- Module 11 and 12 --------------------- */

.zhi-module-11 .zhi-module-entry-text {

  margin: 0 auto; }

.zhi-module-11 .entry-summary:before {

  width: 35px;

  height: 1px;

  background: #aaa;

  display: block;

  content: "";

  text-align: center;

  margin: 0 auto 15px; }

.zhi-dark-bg .zhi-module-11 .entry-summary:before {

  background: #505050; }

.zhi-module-11 .zhi-read-more {

  margin: 20px 0 0 0; }

.zhi-module-11 .zhi-read-more a {

  color: #555;

  font-size: 14px;

  line-height: 1;

  padding: 10px 12px;

  border: 2px solid #888;

  display: inline-block;

  margin-top: 10px;

  -webkit-transition: all 0.3s ease-in-out 0s;

  transition: all 0.3s ease-in-out 0s; }

  .zhi-module-11 .zhi-read-more a:hover {

    background: #333;

    border-color: #333;

    color: #fff; }



.zhi-module-11 .zhi-module-image .zhi-post-link:after, .zhi-module-12 .zhi-module-image .zhi-post-link:after, .zhi-module-13 .zhi-module-image .zhi-post-link:after {

  content: '';

  position: absolute;

  left: 0;

  top: 0;

  overflow: hidden;

  width: 100%;

  height: 100%;

  background: rgba(0, 0, 0, 0.6);

  -webkit-transition: opacity 0.4s ease-in-out 0s;

  transition: opacity 0.4s ease-in-out 0s;

  opacity: 0; }

.zhi-module-11 .zhi-module-image:hover .zhi-post-link:after, .zhi-module-12 .zhi-module-image:hover .zhi-post-link:after, .zhi-module-13 .zhi-module-image:hover .zhi-post-link:after {

  opacity: 1; }

.zhi-module-11 .zhi-module-image img:hover, .zhi-module-12 .zhi-module-image img:hover, .zhi-module-13 .zhi-module-image img:hover {

  -webkit-filter: brightness(80%);

          filter: brightness(80%); }

.zhi-module-11 .zhi-module-image:hover, .zhi-module-12 .zhi-module-image:hover, .zhi-module-13 .zhi-module-image:hover {

  background: rgba(0, 0, 0, 0.3); }

.zhi-module-11 .zhi-terms, .zhi-module-12 .zhi-terms, .zhi-module-13 .zhi-terms {

  display: block;

  color: #f9f9f9; }

  .zhi-module-11 .zhi-terms a, .zhi-module-12 .zhi-terms a, .zhi-module-13 .zhi-terms a {

    color: #ddd;

    position: relative;

    display: inline;

    zoom: 1;

    font-style: italic;

    -webkit-transition: color 0.3s ease-in-out 0s;

    transition: color 0.3s ease-in-out 0s; }

    .zhi-module-11 .zhi-terms a:hover, .zhi-module-12 .zhi-terms a:hover, .zhi-module-13 .zhi-terms a:hover {

      color: #fff; }

.zhi-module-11 .zhi-module-meta span, .zhi-module-11 .zhi-module-meta span a, .zhi-module-12 .zhi-module-meta span, .zhi-module-12 .zhi-module-meta span a, .zhi-module-13 .zhi-module-meta span, .zhi-module-13 .zhi-module-meta span a {

  font-size: 13px;

  line-height: 22px; }



.zhi-module-12 .zhi-module-image .zhi-module-thumb {

  margin: 0; }



.zhi-module-13 {

  background: #fff;

  border-radius: 6px 6px 10px 10px;

  border: none;

  padding: 0;

  margin: 0;

  -webkit-transition: -webkit-box-shadow 0.25s ease;

  transition: -webkit-box-shadow 0.25s ease;

  transition: box-shadow 0.25s ease;

  transition: box-shadow 0.25s ease, -webkit-box-shadow 0.25s ease;

  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  overflow: hidden; }

  .zhi-module-13:hover {

    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

  .zhi-module-13 .zhi-module-image .zhi-module-thumb {

    margin: 0; }

  .zhi-module-13 .zhi-module-entry-text {

    margin: 0;

    padding: 25px 20px; }

  .zhi-module-13 .zhi-read-more {

    margin: 15px 0 0 0; }

    .zhi-module-13 .zhi-read-more a {

      color: #333;

      font-size: 12px;

      line-height: 1;

      font-weight: 600;

      text-transform: uppercase;

      display: inline-block;

      padding: 0;

      -webkit-transition: color 0.3s ease-in-out 0s;

      transition: color 0.3s ease-in-out 0s; }

      .zhi-module-13 .zhi-read-more a:hover {

        color: #666; }

      .zhi-module-13 .zhi-read-more a:after {

        content: '›';

        display: inline-block;

        margin-left: 7px; }

  .zhi-dark-bg .zhi-module-13 .entry-title a {

    color: #333; }

  .zhi-dark-bg .zhi-module-13 .zhi-module-meta span {

    color: #999; }

    .zhi-dark-bg .zhi-module-13 .zhi-module-meta span a {

      color: #444; }

  .zhi-dark-bg .zhi-module-13 .entry-summary {

    color: #686868; }



/*# sourceMappingURL=zhi-blocks.css.map */