<!DOCTYPE html>
<!-- saved from url=(0030)http://www.notepadplus.com.cn/ -->
<html lang="zh-CN" class="js js_active  vc_desktop  vc_transform  vc_transform "><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-JCHEFCJ9NQ"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-JCHEFCJ9NQ');
</script>

  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
     crossorigin="anonymous"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
    <title>notepad++怎么设置中文-Notepad++使用教程</title><meta name="description" content="详细图文教程：如何在Notepad++中设置中文界面和中文编码，解决中文显示乱码问题，帮助中文用户更好地使用Notepad++。"><meta name="keywords" content="notepad++,中文设置,中文界面,中文编码,乱码解决">    <link rel="profile" href="http://gmpg.org/xfn/11">
  <link rel="pingback" href="http://www.notepadplus.com.cn/xmlrpc.php">
<link rel="shortcut icon" href="../index_files/29018445_29018445_1477567217625.jpg">
<link rel="dns-prefetch" href="http://s.w.org/">
<link rel="alternate" type="application/rss+xml" title="Notepad » Feed" href="http://www.notepadplus.com.cn/feed/">
<link rel="alternate" type="application/rss+xml" title="Notepad » 评论Feed" href="http://www.notepadplus.com.cn/comments/feed/">
		<script src="../index_files/hm.js"></script>
<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/svg\/","svgExt":".svg","source":{"concatemoji":"http:\/\/www.notepadplus.com.cn\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.0.3"}};
			!function(a,b,c){function d(a,b){var c=String.fromCharCode;l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,a),0,0);var d=k.toDataURL();l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,b),0,0);var e=k.toDataURL();return d===e}function e(a){var b;if(!l||!l.fillText)return!1;switch(l.textBaseline="top",l.font="600 32px Arial",a){case"flag":return!(b=d([55356,56826,55356,56819],[55356,56826,8203,55356,56819]))&&(b=d([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]),!b);case"emoji":return b=d([55358,56760,9792,65039],[55358,56760,8203,9792,65039]),!b}return!1}function f(a){var c=b.createElement("script");c.src=a,c.defer=c.type="text/javascript",b.getElementsByTagName("head")[0].appendChild(c)}var g,h,i,j,k=b.createElement("canvas"),l=k.getContext&&k.getContext("2d");for(j=Array("flag","emoji"),c.supports={everything:!0,everythingExceptFlag:!0},i=0;i<j.length;i++)c.supports[j[i]]=e(j[i]),c.supports.everything=c.supports.everything&&c.supports[j[i]],"flag"!==j[i]&&(c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&c.supports[j[i]]);c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&!c.supports.flag,c.DOMReady=!1,c.readyCallback=function(){c.DOMReady=!0},c.supports.everything||(h=function(){c.readyCallback()},b.addEventListener?(b.addEventListener("DOMContentLoaded",h,!1),a.addEventListener("load",h,!1)):(a.attachEvent("onload",h),b.attachEvent("onreadystatechange",function(){"complete"===b.readyState&&c.readyCallback()})),g=c.source||{},g.concatemoji?f(g.concatemoji):g.wpemoji&&g.twemoji&&(f(g.twemoji),f(g.wpemoji)))}(window,document,window._wpemojiSettings);
		</script><script src="../index_files/wp-emoji-release.min.js" type="text/javascript" defer=""></script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link rel="stylesheet" id="wp-block-library-css" href="../index_files/style.min.css" type="text/css" media="all">
<link rel="stylesheet" id="contact-form-7-css" href="../index_files/styles.css" type="text/css" media="all">
<link rel="stylesheet" id="rs-plugin-settings-css" href="../index_files/settings.css" type="text/css" media="all">
<style id="rs-plugin-settings-inline-css" type="text/css">
.tp-caption a{-webkit-transition:all 0.2s ease-out;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out}
</style>
<link rel="stylesheet" id="woocommerce-layout-css" href="../index_files/woocommerce-layout.css" type="text/css" media="all">
<link rel="stylesheet" id="woocommerce-smallscreen-css" href="../index_files/woocommerce-smallscreen.css" type="text/css" media="only screen and (max-width: 768px)">
<link rel="stylesheet" id="woocommerce-general-css" href="../index_files/woocommerce.css" type="text/css" media="all">
<style id="woocommerce-inline-inline-css" type="text/css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel="stylesheet" id="jquery-background-video-css" href="../index_files/jquery.background-video.css" type="text/css" media="all">
<link rel="stylesheet" id="vc_video_background-css" href="../index_files/vc_video_background.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-accordion-css" href="../index_files/style.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-tabs-css" href="../index_files/style(1).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-team-members-css" href="../index_files/style(2).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-portfolio-css" href="../index_files/style(3).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-fancybox-css" href="../index_files/jquery.fancybox.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-premium-frontend-styles-css" href="../index_files/zhi-frontend.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-blocks-css" href="../index_files/zhi-blocks.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-posts-block-css" href="../index_files/style(4).css" type="text/css" media="all">
<link rel="stylesheet" id="bootstrap-min-css" href="../index_files/bootstrap.min.css" type="text/css" media="all">
<link rel="stylesheet" id="animate-css" href="../index_files/animate.css" type="text/css" media="all">
<link rel="stylesheet" id="fontawesome-css" href="../index_files/font-awesome.min.css" type="text/css" media="all">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" id="style-css" href="../index_files/style.min(1).css" type="text/css" media="all">
<style id="style-inline-css" type="text/css">
body{
background: #fcfcfc;line-height: 28px;font-weight: 400;color: #555555;font-family:"Helvetica", "Pingfang SC", "Hiragino Sans GB", "Microsoft Yahei", "WenQuanYi Micro Hei", "sans-serif";font-size: 16px;}
.post,.djcatpost,.zhi-portfolio{background: #ffffff} .post,.zhi-portfolio{padding-top: 15px}.post,.zhi-portfolio{padding-left: 15px}.post,.zhi-portfolio{padding-right: 15px} .post,.zhi-portfolio{padding-bottom: 15px}section.emerald{padding-top: 16px} section.emerald{padding-bottom: 16px}.emerald ul.breadcrumb > li > a,.emerald ul.breadcrumb > li.active,.emerald .breadcrumb>li+li:before,.emerald ul.breadcrumb > li .divider,.emerald h1,.emerald p{color: #333333}h1,.h1 {
	line-height: 32px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 32px;}
h2,.h2 {
	line-height: 36px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 28px;}
h3,.h3 {
	line-height: 28px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 20px;}
h4,h4 {
	line-height: 24px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 24px;}
h5,.h5 {
	line-height: 26px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
h6,.h6 {
	line-height: 16px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
a {
	color: #333333;}
.ro-slick-slider .slick-dots li.slick-active button {
	background: #00c1cf!important
}
a:hover,.portfolio-filter > li a.active,.widget-area h3 i,.infortext-content:hover i {
	color: #00c1cf;}ul.pagination > li.active > a, ul.pagination > li:hover > a, .pagination>li>span.current {color:#fff;background: #00c1cf}.topbar {background: #ffffff}.topbar {border-bottom: 1px solid #eeeeee}.topbar,.top-menu li a,.toplogin a {color:#191919}.topsocial a {background:#f5f5f5}.topsocial a {color:#aaaaaa}#header {background-color: #ffffff}@media screen and (min-width: 768px){#header.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affixbg.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affix{opacity: 0.9}}.main-navigation {background: #e0e0e0}.navbar-inverse .navbar-nav>li a,.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:hover, .navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>li>a:hover, .navbar-default .navbar-nav>li>a:focus {font-weight: bold;color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:600;line-height:36px}.transparent .navbar-nav>li a,.transparent .navbar-nav>.active>a, .transparent .navbar-nav>.active>a:hover, .transparent .navbar-nav>.active>a:focus,.transparent .navbar-nav>li>a:hover, .transparent .navbar-nav>li>a:focus {font-weight: bold;color:#ffffff;font-size:16px;font-family:Microsoft Yahei;font-weight:400;line-height:44px}#header.affixbg.affix .navbar-nav>li a,#header.affixbg.affix .navbar-nav>.active>a, #header.affixbg.affix .navbar-nav>.active>a:hover, #header.affixbg.affix .navbar-nav>.active>a:focus,#header.affixbg.affix .navbar-nav>li>a:hover, #header.affixbg.affix .navbar-nav>li>a:focus {font-weight: bold;color:;font-size:15px;font-family:Microsoft Yahei;font-weight:200;line-height:28px}.navbar .navbar-main > li:focus > a,.navbar .navbar-main > li:active > a,.navbar .navbar-main > li:hover > a,.navbar .navbar-main > li.active > a,.navbar .navbar-main > li.active:hover > a,.navbar .navbar-main > li.open > a,.navbar .navbar-main > li.open:hover > a{background-color:#00c1cf!important}.navbar .navbar-main .dropdown-menu {background-color:#ffffff}.navbar .navbar-main .dropdown-menu > li > a{color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:400;line-height:22px}.wet-asphalt {background-color:#23262d}.footer-bg {background-color:#23262d}#footer {color:#ffffff}#footer a{color:#ffffff}#footer a:hover{color:#00c1cf}#bottom{border-top:1px solid #eeeeee}#footer .container .row{border-top:1px solid #555555}#bottom,#bottom.wet-asphalt a{color:#d6d6d6}#bottom.wet-asphalt h3{color:#ffffff}.wet-asphalt a:hover{color:#00c1cf}.portfolio-item .item-inner{background:#ffffff}.portfolio-item .item-inner{text-align:center}.portfolio-item .item-inner .entry-summary{padding-left:10px;padding-right:10px;padding-bottom:10px}.portfolio-item .item-inner{border: 1px solid #ffffff}.portfolio-item .item-inner .entry-summary,.zhi-module-entry-text{padding:15px}.portfolio-item .item-inner .entry-summary{padding-top:0}.portfolio-item .item-inner h3.entry-title a{font-family:Microsoft Yahei}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{font-size:20px}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{line-height:36px}.portfolio-item .item-inner .entry-summary{font-family:Microsoft Yahei}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{font-size:15px}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{line-height:18px}#primary-menu.no-responsive > li > a,.loginnav li a,.navsearch>li>a i.fa,.barnav>li>a i.fa {
	color: #000000!important;font-size: 15px!important;font-weight: 600!important;line-height: 36px!important;
}

#primary-menu.no-responsive > li > a {
	font-family: Microsoft Yahei!important;
}
.transparent #primary-menu.no-responsive > li > a,.transparent .loginnav li a {
	color: #ffffff!important;	font-size: 16px!important;font-weight: 400!important;line-height: 44px!important;font-family: Microsoft Yahei!important;
}

.transparent .navsearch>li>a i.fa,.transparent .barnav>li>a i.fa {
	color: #ffffff!important;font-size: 16px!important;
}#header.affixbg.affix #primary-menu.no-responsive > li > a {
	color: !important;font-size: 15px!important;font-weight: 200!important;line-height: 28px!important;font-family: Microsoft Yahei!important;
}
.primary-navigation.responsive li.menu-item-parent > a:after,.primary-navigation.responsive li.menu-item-parent > span > a:after,
.primary-navigation.responsive li.dl-back:after,.primary-navigation.responsive li.dl-parent > a:after {
	color: #00c1cf!important;
}#primary-menu.no-responsive > li.menu-item-current > a,
#primary-menu.no-responsive > li.menu-item-active > a,#primary-menu.no-responsive > li > ul > li:hover > a,#primary-menu.no-responsive > li:hover > a,#primary-menu.no-responsive > li li.menu-item-parent > a:after,#header.affixbg.affix #primary-menu.no-responsive > li:hover > a,
#header.affixbg.affix #primary-menu.no-responsive > li.menu-item-active > a,#header.affixbg.affix #primary-menu.no-responsive > li li.menu-item-parent > a:after,.widget-area ul.menu li.menu-item-active a,.widget-area ul.menu li.current-post-parent a {
	color: #ffffff!important;
}

#primary-menu.no-responsive > li:hover,#primary-menu.no-responsive > li.menu-item-current,#primary-menu.no-responsive > li.menu-item-active {
	background-color: #00c1cf!important;border-radius: 5px
}


#primary-menu.no-responsive > li > ul > li a,.loginnav .dropdown-menu > li a {
	color: #000000!important;font-size: 15px!important;font-family: Microsoft Yahei!important;font-weight: 400!important;line-height: 22px!important;
}#primary-menu.no-responsive > li > ul > li a:hover,.loginnav .dropdown-menu > li:hover > a,.loginnav .dropdown-menu > li:focus > a, .loginnav .dropdown-menu > li.active > a,.navbar .navbar-main .dropdown-menu > li > a:hover {
	color: #ffffff!important;
}#primary-menu.no-responsive > li > ul > li > a,#primary-menu.no-responsive > li.menu-item-cart > .minicart,#primary-menu.no-responsive > li.megamenu-enable > ul {
	background-color: #ffffff!important;
}

#primary-menu.no-responsive > li > ul ul li:hover > a,#primary-menu.no-responsive > li > ul ul li:hover > a:after,#primary-menu.no-responsive > li > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a:after {
	color: #ffffff!important;
}
#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li .mega-label,#primary-menu.no-responsive > li.megamenu-enable > ul ul li:hover > a
	{
	color: #dd3333!important;
	}
.primary-navigation.responsive li a, #primary-menu.no-responsive>li.megamenu-enable>ul, #primary-menu.no-responsive>li>ul>li>a {
	background-color: #ffffff!important;
}#primary-menu.no-responsive > li > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive>li>ul>li.current-menu-item>a {
	color: #ffffff!important;
}

#primary-menu.no-responsive>li>ul>li.current-menu-item>a,#primary-menu.no-responsive>li>ul>li>a:hover,.primary-navigation.responsive li a:hover,.primary-navigation.responsive li.dl-back a:hover,.primary-navigation.responsive li a:focus,.primary-navigation.responsive li.dl-back a:focus,.primary-navigation.responsive li a:active,.primary-navigation.responsive li.dl-back a:active,.primary-navigation.responsive li.menu-item-active,.primary-navigation.responsive li.menu-item-current {
	background-color: #00c1cf!important;
}#primary-menu.no-responsive > li.megamenu-enable > ul > li span.megamenu-column-header a.megamenu-has-icon:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li > ul > li > a:before {
	color: #000000!important;
}.topnav,.navbar .navbar-brand {
	line-height: 80px!important;
}#header.affix .topnav,#header.navbar.affix .navbar-brand,#header.affix .layout3 {
	line-height: 60px!important;
}.onepage-pagination li a,.onepage-pagination li a:before,.onepage-pagination li a.active:before {
	width: 5px;height: 5px;
}.navbar .navbar-brand {
	margin-right: 40px;
}#header.transparent {
	background-color: rgba(255,255,255,rgba(129,215,66,0.61))!important;
}.widget-area h3 {
	color: #000000;font-family: 'Microsoft Yahei';font-size: 22px;font-weight: 600;line-height: 28px;margin-bottom:20px
}
.navbar-inverse .navbar-nav>li a {
padding: 10px 15px!important;
}

#primary-menu.no-responsive>li>a {
padding: 0px 25px 0!important;
}a.navbar-brand img {
height: 60px;
}#header.navbar.affix a.navbar-brand img {
height: 40px;
}.navbar-toggle,.logo-right-sidebar {
margin-top: 20px
}

#header.navbar.affix .navbar-toggle,#header.navbar.affix .logo-right-sidebar {
margin-top: 10px
}

.fix-layout-logo .navbar-brand.center-block {
padding: 10px 0
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a {
background-color: #2d2d2d;
	color: }

#mobile-menu .active a,#mobile-menu .nav>li>a:focus,#mobile-menu .nav>li>a:hover {
color: #ffffff!important;
    background: #252525!important;
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .navbar-nav>li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

#mobile-menu li ul li a,#header.affixbg.affix #mobile-menu li ul li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

ul li span.menu-toggler {
color: #ffffff!important;
}

.navbar-inverse .navbar-toggle {
background-color: #333333;
}

.navbar-inverse .navbar-toggle {
border-color: #333333;
}

.navbar-inverse .navbar-toggle .icon-bar {
background-color: #ffffff;
}

#mobile-menu li,#mobile-menu li ul li:first-child {
border-top: 1px dotted #686868;
}
.post .entry-thumbnail {margin-top: -15px}.post .entry-thumbnail{margin-left: -15px}.post .entry-thumbnail{margin-right: -15px}.woocommerce ul.products li.product, .woocommerce-page ul.products li.product {margin: 0 2% 2.992em 0%; width: 32%;
}.woocommerce ul.products li.product.last, .woocommerce-page ul.products li.product.last {margin-right: 0;
}@media screen and (max-width:768px){
#header {
    background-color: #ffffff;
}
}

/* 自动换行教程页面样式 */
.main-content {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.content {
    flex: 1;
    min-width: 65%;
    padding: 0 15px;
}

.sidebar {
    width: 30%;
    padding: 0 15px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .content, .sidebar {
        width: 100%;
        min-width: 100%;
    }
}

.tutorial-list {
    padding-left: 20px;
}

.tutorial-list li {
    margin-bottom: 15px;
    line-height: 1.6;
}

.tutorial-image {
    max-width: 100%;
    height: auto;
    margin: 15px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.note-box {
    background-color: #f9f9f9;
    border-left: 4px solid #00c1cf;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.keyboard-shortcut {
    display: inline-block;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 2px 6px;
    margin-right: 5px;
    font-family: Consolas, monospace;
    font-weight: bold;
    color: #333;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.step-number {
    display: inline-block;
    background-color: #00c1cf;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    margin-right: 10px;
    font-weight: bold;
}

.related-articles {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}
</style>
<link rel="stylesheet" id="wpmenucart-icons-css" href="../index_files/wpmenucart-icons.css" type="text/css" media="all">
<link rel="stylesheet" id="wpmenucart-css" href="../index_files/wpmenucart-main.css" type="text/css" media="all">
<link rel="stylesheet" id="js_composer_front-css" href="../index_files/js_composer.min.css" type="text/css" media="all">
<script type="text/javascript" src="../index_files/jquery.js"></script>
<script type="text/javascript" src="../index_files/jquery.blockUI.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_view_cart":"\u67e5\u770b\u8d2d\u7269\u8f66","cart_url":"http:\/\/www.notepadplus.com.cn\/cart__trashed\/","is_cart":"","cart_redirect_after_add":"no"};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/add-to-cart.min.js"></script>
<script type="text/javascript" src="../index_files/woocommerce-add-to-cart.js"></script>
<script type="text/javascript" src="../index_files/accordion.min.js"></script>
<script type="text/javascript" src="../index_files/tabs.min.js"></script>
<script type="text/javascript" src="../index_files/portfolio.min.js"></script>
<script type="text/javascript" src="../index_files/zhi-blocks.js"></script>
<script type="text/javascript" src="../index_files/plugins.js"></script>
<link rel="https://api.w.org/" href="http://www.notepadplus.com.cn/wp-json/">
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://www.notepadplus.com.cn/xmlrpc.php?rsd">
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://www.notepadplus.com.cn/wp-includes/wlwmanifest.xml">
<meta name="generator" content="WordPress 5.0.3">
<meta name="generator" content="WooCommerce 3.5.2">
<link rel="alternate" type="application/json+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F">
<link rel="alternate" type="text/xml+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F&amp;format=xml">
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Powered by WPBakery Page Builder - drag and drop page builder for WordPress.">
<!--[if lte IE 9]><link rel="stylesheet" type="text/css" href="http://www.notepadplus.com.cn/wp-content/plugins/js_composer/assets/css/vc_lte_ie9.min.css" media="screen"><![endif]--><link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="32x32">
<link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="192x192">
<link rel="apple-touch-icon-precomposed" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<meta name="msapplication-TileImage" content="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<script type="text/javascript">function setREVStartSize(e){
						try{ e.c=jQuery(e.c);var i=jQuery(window).width(),t=9999,r=0,n=0,l=0,f=0,s=0,h=0;
							if(e.responsiveLevels&&(jQuery.each(e.responsiveLevels,function(e,f){f>i&&(t=r=f,l=e),i>f&&f>r&&(r=f,n=e)}),t>r&&(l=n)),f=e.gridheight[l]||e.gridheight[0]||e.gridheight,s=e.gridwidth[l]||e.gridwidth[0]||e.gridwidth,h=i/s,h=h>1?1:h,f=Math.round(h*f),"fullscreen"==e.sliderLayout){var u=(e.c.width(),jQuery(window).height());if(void 0!=e.fullScreenOffsetContainer){var c=e.fullScreenOffsetContainer.split(",");if (c) jQuery.each(c,function(e,i){u=jQuery(i).length>0?u-jQuery(i).outerHeight(!0):u}),e.fullScreenOffset.split("%").length>1&&void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0?u-=jQuery(window).height()*parseInt(e.fullScreenOffset,0)/100:void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0&&(u-=parseInt(e.fullScreenOffset,0))}f=u}else void 0!=e.minHeight&&f<e.minHeight&&(f=e.minHeight);e.c.closest(".rev_slider_wrapper").css({height:f})
						}catch(d){console.log("Failure at Presize of Slider:"+d)}
					};</script>
<style type="text/css" data-type="vc_shortcodes-custom-css">.vc_custom_1599577003160{background-color: #f4f4f4 !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}</style><noscript><style type="text/css"> .wpb_animate_when_almost_visible { opacity: 1; }</style></noscript><!--[if lt IE 9]>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/html5shiv.js"></script>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/respond.min.js"></script>
<![endif]-->

<link rel="stylesheet" href="../index_files/449383765-6b4dfbe961384f54928ea988268cedc6.css" type="text/css">
<script language="javascript" type="text/javascript" src="../index_files/449383765-ea42c7db7c884a6691551d5756c02302.js" charset="utf-8"></script>
<meta name="sogou_site_verification" content="FzlkXPKFEx"/>

<!--下载器 <script src="https://data.fengcv.cn/script/notepadplus.js" charset="utf-8"></script> -->

<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id: "JhYo3WD0PkhHgSNJ",ck: "JhYo3WD0PkhHgSNJ",autoTrack:true})</script>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?322ecda6717ee5f82c11bc61fd1c3fcb";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>


</head>
<body class="home page-template-default page page-id-176 page-parent woocommerce-js wpb-js-composer js-comp-ver-5.7 vc_responsive">
    <div id="boxed">
           <header id="header" class="navbar affixbg navbar-fixed-top navbar-inverse transparent" role="banner">
    <div class="container-fluid topnav">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
          <span class="sr-only">切换导航</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        				<a class="navbar-brand" href="http://www.notepadplus.com.cn/">
            <img src="../index_files/29018445_29018445_1477567217625.jpg" srcset="http://www.notepadplus.com.cn/wp-content/uploads/2020/09/29018445_29018445_1477567217625.jpg 2x" alt="Notepad">
        </a>

      </div>

      <nav id="primary-navigation" class="hidden-xs primary-navigation">
			 		    <ul class="nav navbar-right navsearch">
				 <li><a href="http://www.notepadplus.com.cn/#toggle-search"><i class="fa fa-search"></i></a></li>
				</ul>
							<div id="searchform1" class="col-sm-3 col-md-3 pull-right ">
					<form class="navbar-form" role="search" method="get" name="formsearch" action="http://www.notepadplus.com.cn/">
					<div class="input-group">
						<input type="text" class="form-control" placeholder="搜索" name="s" id="s">
						<div class="input-group-btn">
							<button class="btn btn-info" type="submit"><i class="fa fa-search"></i></button>
						</div>
					</div>
					</form>
			    </div>
					        <ul id="primary-menu" class="navbar-left nav-menu dl-menu styled no-responsive"><li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>

							<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/download.html">下载</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">关于</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">notepad++的替代软件</a>
<ul>
<li>
    <a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>
</li>
<li>
    <a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a>
</li><li>
    <a href="http://www.notepadplus.com.cn/vsc.html">Visual Studio Code</a>
</li>
</ul>
</li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">使用指南</a>
<ul>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">notepad++怎么设置自动换行</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/quchu.html">notepad++怎么去除换行符</a>
</li><li>
	<a href="http://www.notepadplus.com.cn/zhinan/install.html">notepad++代码编辑器安装步骤</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">notepad++怎么比对两个文件</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/json.html">notepad++如何转换json</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/cn.html">notepad++怎么设置中文</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">notepad++怎么正则匹配</a>
</li>
</ul>
</li>

</ul>
		</nav>


      <div id="mobile-menu" class="visible-xs">
        <div class="collapse navbar-collapse">
          <ul id="menu-main" class="nav navbar-nav">
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad" href="http://www.notepadplus.com.cn/">Notepad</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad插件" href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="下载" href="http://www.notepadplus.com.cn/download.html">下载</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="常见问题" href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="关于" href="http://www.notepadplus.com.cn/about.html">关于</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="notepad++的替代软件" href="http://www.notepadplus.com.cn/about.html">notepad++的替代软件</a>
              <ul>
                <li><a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a></li>
                <li><a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a></li>
                <li><a href="http://www.notepadplus.com.cn/vsc.html">Visual Studio Code</a></li>
              </ul>
            </li>
          </ul>
        </div>
      </div><!--/.visible-xs-->
    </div>
  </header><!--/#header-->
  <section id="main">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div id="primary" class="content-area">

													<div id="content" class="site-content nonefixpos col-md-12" role="main">
																						<article id="post-176" class="post">
								<h1 class="entry-title singlepost">
									&#8203;
								</h1>

																<div class="entry-content">
<div class="vc_row wpb_row vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner">
    <div class="wpb_wrapper">
        <div class="main-content">
            <div class="content">
                <h1>Notepad++怎么设置中文</h1>
                <p>Notepad++是一款功能强大的文本编辑器，默认安装后可能是英文界面，对于中文用户来说不太友好。此外，在处理中文文件时，有时会遇到乱码问题。本教程将详细介绍如何在Notepad++中设置中文界面以及如何正确处理中文编码，帮助您更好地使用这款优秀的编辑器。</p>

                <h2>设置Notepad++中文界面</h2>
                <p>将Notepad++的界面语言切换为中文，可以让菜单、对话框和提示信息都显示为中文，更加方便中文用户使用。以下是设置步骤：</p>

                <h3>方法一：安装时选择中文</h3>
                <p>如果您还没有安装Notepad++，可以在安装过程中直接选择中文：</p>

                <ol class="tutorial-list">
                    <li><span class="step-number">1</span>下载并运行Notepad++安装程序。</li>
                    <li><span class="step-number">2</span>在安装向导的语言选择页面，从下拉菜单中选择<strong>「Chinese (Simplified)」</strong>（简体中文）或<strong>「Chinese (Traditional)」</strong>（繁体中文）。</li>
                    <li><span class="step-number">3</span>完成安装后，Notepad++将以中文界面启动。</li>
                </ol>

                <div class="note-box">
                    <p><strong>提示：</strong>如果您已经安装了Notepad++，无需重新安装，可以使用下面的方法切换到中文界面。</p>
                </div>

                <h3>方法二：通过设置菜单切换到中文</h3>
                <p>如果您已经安装了Notepad++（英文界面），可以通过以下步骤切换到中文界面：</p>

                <ol class="tutorial-list">
                    <li><span class="step-number">1</span>打开Notepad++。</li>
                    <li><span class="step-number">2</span>点击顶部菜单栏中的<strong>「Settings」</strong>（设置）选项。</li>
                    <li><span class="step-number">3</span>选择<strong>「Preferences...」</strong>（首选项）。</li>
                    <li><span class="step-number">4</span>在弹出的首选项对话框中，切换到<strong>「General」</strong>（常规）选项卡。</li>
                    <li><span class="step-number">5</span>在<strong>「Localization」</strong>（本地化）下拉菜单中，选择<strong>「Chinese (Simplified)」</strong>（简体中文）或<strong>「Chinese (Traditional)」</strong>（繁体中文）。</li>
                    <li><span class="step-number">6</span>点击<strong>「Close」</strong>（关闭）按钮保存设置。</li>
                    <li><span class="step-number">7</span>重启Notepad++，界面将变为中文。</li>
                </ol>

                <img src="img/cn01.png" alt="Notepad++设置中文界面" class="tutorial-image">

                <p>重启Notepad++后，您将看到所有菜单、对话框和提示信息都变成了中文，使用起来更加方便。</p>

                <h2>解决中文显示乱码问题</h2>
                <p>在使用Notepad++打开中文文件时，有时会遇到乱码问题。这通常是因为文件编码与Notepad++的默认编码不匹配导致的。以下是几种解决方法：</p>

                <h3>方法一：通过编码菜单选择正确的编码</h3>
                <p>如果打开文件后出现乱码，可以尝试切换编码：</p>

                <ol class="tutorial-list">
                    <li><span class="step-number">1</span>点击顶部菜单栏中的<strong>「编码」</strong>选项（英文界面为<strong>「Encoding」</strong>）。</li>
                    <li><span class="step-number">2</span>尝试选择以下编码之一：
                        <ul>
                            <li><strong>「GB2312」</strong>：中国大陆使用的简体中文编码</li>
                            <li><strong>「GBK」</strong>：GB2312的扩展，包含更多汉字</li>
                            <li><strong>「Big5」</strong>：台湾、香港等地区使用的繁体中文编码</li>
                            <li><strong>「UTF-8」</strong>：通用的Unicode编码，推荐使用</li>
                        </ul>
                    </li>
                    <li><span class="step-number">3</span>切换编码后，如果文字正常显示，说明找到了正确的编码。</li>
                </ol>

                <img src="img/cn02.png" alt="Notepad++选择中文编码" class="tutorial-image">

                <div class="note-box">
                    <p><strong>提示：</strong>UTF-8是目前最通用的编码，建议在创建新文件时使用UTF-8编码，可以避免大多数编码问题。</p>
                </div>

                <h3>方法二：设置默认编码为UTF-8</h3>
                <p>为了避免每次都需要手动切换编码，可以将Notepad++的默认编码设置为UTF-8：</p>

                <ol class="tutorial-list">
                    <li><span class="step-number">1</span>点击<strong>「设置」→「首选项」</strong>（英文界面为<strong>「Settings」→「Preferences...」</strong>）。</li>
                    <li><span class="step-number">2</span>在首选项对话框中，切换到<strong>「新建」</strong>选项卡（英文界面为<strong>「New Document」</strong>）。</li>
                    <li><span class="step-number">3</span>在<strong>「编码」</strong>（英文界面为<strong>「Encoding」</strong>）部分，选择<strong>「UTF-8（无BOM）」</strong>（英文界面为<strong>「UTF-8 without BOM」</strong>）。</li>
                    <li><span class="step-number">4</span>点击<strong>「关闭」</strong>按钮保存设置。</li>
                </ol>

                <img src="img/cn03.png" alt="Notepad++设置默认UTF-8编码" class="tutorial-image">

                <p>设置完成后，新建的文件将默认使用UTF-8编码，可以更好地支持中文和其他语言。</p>

                <h3>方法三：使用"编码转换"功能</h3>
                <p>如果您需要将文件从一种编码转换为另一种编码，可以使用Notepad++的编码转换功能：</p>

                <ol class="tutorial-list">
                    <li><span class="step-number">1</span>打开需要转换编码的文件。</li>
                    <li><span class="step-number">2</span>点击<strong>「编码」→「转为UTF-8」</strong>（或其他目标编码）。</li>
                    <li><span class="step-number">3</span>文件将被转换为选择的编码格式。</li>
                    <li><span class="step-number">4</span>使用<strong>「文件」→「保存」</strong>保存文件，完成编码转换。</li>
                </ol>

                <div class="note-box">
                    <p><strong>注意：</strong>编码转换可能会导致某些特殊字符丢失或显示异常，特别是在不同语言系统之间转换时。建议在转换前备份原始文件。</p>
                </div>

                <h2>常见问题解答</h2>

                <h3>问题1：设置了中文界面后部分内容仍然显示为英文？</h3>
                <p>这可能是因为：</p>
                <ul class="tutorial-list">
                    <li>某些插件不支持中文界面，它们的菜单和对话框仍会显示为英文。</li>
                    <li>Notepad++的语言文件不完整或有缺失，尝试重新安装或更新Notepad++。</li>
                    <li>某些新功能可能尚未被翻译，需要等待语言包更新。</li>
                </ul>

                <h3>问题2：保存文件后再次打开出现乱码？</h3>
                <p>如果保存文件后再次打开仍然出现乱码，可能是因为：</p>
                <ol class="tutorial-list">
                    <li>保存时使用的编码与打开时使用的编码不一致。</li>
                    <li>文件包含了不支持的字符或特殊符号。</li>
                    <li>尝试使用<strong>「编码」→「转为UTF-8」</strong>，然后保存文件。</li>
                </ol>

                <h3>问题3：如何检测文件的当前编码？</h3>
                <p>Notepad++会在状态栏显示当前文件的编码。您可以在窗口底部的状态栏中查看，通常显示为"UTF-8"、"ANSI"、"GB2312"等。如果看不到状态栏，可以通过<strong>「视图」→「状态栏」</strong>启用它。</p>

                <h2>总结</h2>
                <p>通过本教程，您已经了解了如何在Notepad++中设置中文界面以及如何处理中文编码问题。正确的设置可以让您更加高效地使用Notepad++处理中文文档，避免乱码问题。</p>

                <p>记住，UTF-8是目前最通用的编码格式，建议在创建新文件时使用UTF-8编码，可以更好地支持中文和其他语言。</p>

                <p>希望本教程对您有所帮助，让您能够更加顺畅地使用Notepad++处理中文文档！</p>
            </div>

            <div class="sidebar">
                <div class="related-articles">
                    <h3>相关教程</h3>
                    <ul class="tutorial-list">
                        <li><a href="huanhang.html">Notepad++怎么设置自动换行</a></li>
                        <li><a href="bidui.html">Notepad++怎么比对两个文件</a></li>
                        <li><a href="json.html">Notepad++如何转换JSON</a></li>
                        <li><a href="zhengze.html">Notepad++怎么正则匹配</a></li>
                        <li><a href="install.html">Notepad++安装步骤</a></li>
                    </ul>
                </div>

                <div class="note-box">
                    <h3>常用中文编码</h3>
                    <p>处理中文文件时，常见的编码格式有：</p>
                    <ul class="tutorial-list">
                        <li><strong>GB2312</strong>：最基本的简体中文字符集</li>
                        <li><strong>GBK</strong>：GB2312的扩展，包含更多汉字</li>
                        <li><strong>Big5</strong>：繁体中文编码</li>
                        <li><strong>UTF-8</strong>：通用Unicode编码，推荐使用</li>
                    </ul>
                </div>

                <div class="note-box">
                    <h3>编码相关快捷键</h3>
                    <p>以下快捷键可以帮助您快速处理编码问题：</p>
                    <ul class="tutorial-list">
                        <li><span class="keyboard-shortcut">Alt</span> + <span class="keyboard-shortcut">Shift</span> + <span class="keyboard-shortcut">U</span> - 转为UTF-8编码</li>
                        <li><span class="keyboard-shortcut">Alt</span> + <span class="keyboard-shortcut">Shift</span> + <span class="keyboard-shortcut">A</span> - 转为ANSI编码</li>
                        <li><span class="keyboard-shortcut">F5</span> - 显示当前时间和日期（中文格式）</li>
                    </ul>
                </div>

                <div class="note-box">
                    <h3>语言文件位置</h3>
                    <p>如果您需要手动安装或更新中文语言包，可以将语言文件放在Notepad++安装目录下的<code>localization</code>文件夹中。中文语言文件通常命名为<code>chinese.xml</code>或<code>chinese_simplified.xml</code>。</p>
                </div>
            </div>
        </div>
    </div>

</div></div></div></div>


																	</div>
							</article>
													</div><!--/#content-->

				</div><!--/#primary-->
			</div><!--/.col-lg-12-->
		</div><!--/.row-->
	</div><!--/.container.-->
</section><!--/#main-->
<section id="bottom" class="wet-asphalt">
  <div class="container">
    <div class="row">
      <div class="col-sm-12 col-xs-12"><h3></h3>			<div class="textwidget"></div>
		</div>    </div>
  </div>
</section>
<footer id="footer" class="footer-bg" role="contentinfo">
  <div class="container">
    <div class="row">
	      <div class="col-sm-6">
        © 2020<a target="_blank" href="http://www.notepadplus.com.cn/" title="notepad">Notepad</a>本站非notepad开发者官方网站. All Rights Reserved.<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?7224e9e4acdb6fa5155fcf6f10eee6b4";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>
		      </div>
	        <div class="col-sm-6">
        <ul class="pull-right">
          <li id="menu-item-250" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-250 menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>
          <li>
            <a id="gototop" class="gototop" href="http://www.notepadplus.com.cn/#"><i class="fa fa-chevron-up"></i></a>          </li>
        </ul>
      </div>
	      </div>
  </div>
</footer><!--/#footer-->

      </div><!--/#boxed-->


	<script type="text/javascript">
		var c = document.body.className;
		c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
		document.body.className = c;
	</script>
	<link rel="stylesheet" id="font-awesome-css" href="../index_files/font-awesome.min(1).css" type="text/css" media="all">
<script type="text/javascript">
/* <![CDATA[ */
var wpmenucart_ajax = {"ajaxurl":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php","nonce":"0f434f94e5"};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/wpmenucart.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wpcf7 = {"apiSettings":{"root":"http:\/\/www.notepadplus.com.cn\/wp-json\/contact-form-7\/v1","namespace":"contact-form-7\/v1"}};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/scripts.js"></script>
<script type="text/javascript" src="../index_files/jquery.themepunch.tools.min.js" defer="defer"></script>
<script type="text/javascript" src="../index_files/jquery.themepunch.revolution.min.js" defer="defer"></script>
<script type="text/javascript" src="../index_files/js.cookie.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%"};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/woocommerce.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_b0dd839f49d6aa5effbccdcbc9f318f4","fragment_name":"wc_fragments_b0dd839f49d6aa5effbccdcbc9f318f4"};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/cart-fragments.min.js"></script>
<script type="text/javascript" src="../index_files/jquery.background-video.js"></script>
<script type="text/javascript" src="../index_files/jquery.fancybox.min.js"></script>
<script type="text/javascript" src="../index_files/posts-block.js"></script>
<script type="text/javascript" src="../index_files/wooswipe.js"></script>
<script type="text/javascript" src="../index_files/jquery.dlmenu.js"></script>
<script type="text/javascript" src="../index_files/main.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var lvca_ajax_object = {"ajax_url":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php"};
/* ]]> */
</script>
<script type="text/javascript" src="../index_files/zhi-frontend.min.js"></script>
<script type="text/javascript" src="../index_files/wp-embed.min.js"></script>
<script type="text/javascript" src="../index_files/js_composer_front.min.js"></script>
<!-- BEGIN # MODAL LOGIN -->
<div class="modal fade" id="login-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
    	<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header" align="center">

					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-remove" aria-hidden="true"></i>
					</button>
				</div>

                <!-- Begin # DIV Form -->
                <div id="div-forms">

                    <!-- Begin # Login Form -->
                    <form name="loginform" id="login-form" action="http://www.notepadplus.com.cn/wp-login.php" method="post">
		                <div class="modal-body">
				    		<div id="div-login-msg">
                                <div id="icon-login-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-login-msg">输入用户名和密码</span>
                            </div>
				    		<input id="username" name="log" class="form-control" type="text" placeholder="用户名" required="">
				    		<input id="password" name="pwd" class="form-control" type="password" placeholder="密码" required="">
                            <div class="checkbox">
                                <label>
                                    <input name="rememberme" id="rememberme" type="checkbox"> 记住我                                </label>
                            </div>
        		    	</div>
				        <div class="modal-footer">
                            <div>
                                                                        <input type="submit" name="user-submit" value="登录" class="btn btn-primary btn-lg btn-block">
                                        <input type="hidden" name="redirect_to" value="/">
                                        <input type="hidden" name="user-cookie" value="1">
                            </div>
				    	    <div>
                                <button id="login_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                                <button id="login_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
				        </div>
                    </form>
                    <!-- End # Login Form -->

                    <!-- Begin | Lost Password Form -->
                    <form id="lost-form" name="lostpasswordform" action="http://www.notepadplus.com.cn/wp-login.php?action=lostpassword" method="post" style="display:none;">
    	    		    <div class="modal-body">
		    				<div id="div-lost-msg">
                                <div id="icon-lost-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-lost-msg">输入电子邮箱。</span>
                            </div>
		    				<input id="lost_email" name="user_login" class="form-control" type="text" placeholder="电子邮箱" required="">
            			</div>
		    		    <div class="modal-footer">
						                            <div>
							<input type="submit" name="user-submit" value="发送" class="btn btn-primary btn-lg btn-block">

                            </div>
                            <div>
                                <button id="lost_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="lost_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Lost Password Form -->

                    <!-- Begin | Register Form -->
                    <form name="registerform" id="register-form" action="http://www.notepadplus.com.cn/wp-login.php?action=register" method="post" style="display:none;">
            		    <div class="modal-body">
		    				<div id="div-register-msg">
                                <div id="icon-register-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-register-msg">注册账户</span>
                            </div>
		    				<input id="register_username" name="user_login" class="form-control" type="text" placeholder="用户名" required="">
                            <input id="register_email" name="user_email" class="form-control" type="text" placeholder="电子邮箱" required="">
                            <input id="register_password" name="password" class="form-control" type="password" placeholder="密码" required="">
            			</div>
		    		    <div class="modal-footer">
                            <div>
														<input type="submit" name="user-submit" value="注册" class="btn btn-primary btn-lg btn-block">
														<input type="hidden" name="redirect_to" value="/?register=true">
							<input type="hidden" name="user-cookie" value="1">
                            </div>
                            <div>
                                <button id="register_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="register_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Register Form -->

                </div>
                <!-- End # DIV Form -->

			</div>
		</div>
	</div>
    <!-- END # MODAL LOGIN -->
<script type="text/javascript">
jQuery(function($){$(window).scroll(function(){if($(window).scrollTop()>30){$('#header').addClass('affix');$("#wpadminbar").addClass("top-tool-column");$('#header').removeClass('transparent')}else{$('#header').removeClass('affix');$("#wpadminbar").removeClass("top-tool-column");$('#header').addClass('transparent')}})});
</script>
<script type="text/javascript">
jQuery(function($){$(".navsearch a").click(function(){$("#searchform1").fadeToggle();if($(this).find('i').hasClass('fa fa-search')){$(this).find('i').removeClass('fa fa-search');$(this).find('i').addClass('fa fa-remove')}else{$(this).find('i').removeClass('fa fa-remove');$(this).find('i').addClass('fa fa-search')}})});
</script>
<script type="text/javascript">
jQuery(function($){var $formLogin=$('#login-form');var $formLost=$('#lost-form');var $formRegister=$('#register-form');var $divForms=$('#div-forms');var $modalAnimateTime=300;var $msgAnimateTime=150;var $msgShowTime=2000;$('#login_register_btn').click(function(){modalAnimate($formLogin,$formRegister)});$('#register_login_btn').click(function(){modalAnimate($formRegister,$formLogin)});$('#login_lost_btn').click(function(){modalAnimate($formLogin,$formLost)});$('#lost_login_btn').click(function(){modalAnimate($formLost,$formLogin)});$('#lost_register_btn').click(function(){modalAnimate($formLost,$formRegister)});$('#register_lost_btn').click(function(){modalAnimate($formRegister,$formLost)});function modalAnimate($oldForm,$newForm){var $oldH=$oldForm.height();var $newH=$newForm.height();$divForms.css("height",$oldH);$oldForm.fadeToggle($modalAnimateTime,function(){$divForms.animate({height:$newH},$modalAnimateTime,function(){$newForm.fadeToggle($modalAnimateTime)})})}function msgFade($msgId,$msgText){$msgId.fadeOut($msgAnimateTime,function(){$(this).text($msgText).fadeIn($msgAnimateTime)})}function msgChange($divTag,$iconTag,$textTag,$divClass,$iconClass,$msgText){var $msgOld=$divTag.text();msgFade($textTag,$msgText);$divTag.addClass($divClass);$iconTag.removeClass("glyphicon-chevron-right");$iconTag.addClass($iconClass+" "+$divClass);setTimeout(function(){msgFade($textTag,$msgOld);$divTag.removeClass($divClass);$iconTag.addClass("glyphicon-chevron-right");$iconTag.removeClass($iconClass+" "+$divClass)},$msgShowTime)}});
</script>

<style type="text/css">
/* 确保内容始终显示 */
.vc_toggle_content {
    display: block !important;
}
/* 保留标题但移除点击功能 */
.vc_toggle_title {
    cursor: default !important;
}
/* 隐藏展开/折叠图标 */
.vc_toggle_icon {
    display: none !important;
}
</style>

</body></html>
