.zhi-accordion .zhi-panel-title {

  display: block;

  cursor: pointer; }

.zhi-accordion .zhi-panel-content {

  display: none;

  overflow: hidden; }



.zhi-accordion.zhi-style1 .zhi-panel .zhi-panel-title, .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title {

  -webkit-transition: all .3s ease-in-out 0s;

  transition: all .3s ease-in-out 0s; }

  .zhi-accordion.zhi-style1 .zhi-panel .zhi-panel-title:after, .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title:after {

    font-family: 'FontAwesome' !important;

    speak: none;

    font-style: normal;

    font-weight: normal;

    font-variant: normal;

    text-transform: none;

    line-height: 1;

    -webkit-font-smoothing: antialiased;

    -moz-osx-font-smoothing: grayscale;

    position: absolute;

    content: "\f067";

    right: 30px;

    top: 16px;

    font-size: 14px;

    line-height: 1;

    color: #666;

    font-weight: 300;

    -webkit-transition: all .3s ease-in-out 0s;

    transition: all .3s ease-in-out 0s; }

.zhi-accordion.zhi-style1 .zhi-panel.zhi-active .zhi-panel-title:after, .zhi-accordion.zhi-style3 .zhi-panel.zhi-active .zhi-panel-title:after {

  -webkit-transform: rotate(45deg);

          transform: rotate(45deg); }



.zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title {

  -webkit-transition: all .3s ease-in-out 0s;

  transition: all .3s ease-in-out 0s; }

  .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title:after {

    font-family: 'FontAwesome' !important;

    speak: none;

    font-style: normal;

    font-weight: normal;

    font-variant: normal;

    text-transform: none;

    line-height: 1;

    -webkit-font-smoothing: antialiased;

    -moz-osx-font-smoothing: grayscale;

    position: absolute;

    right: 30px;

    top: 15px;

    content: "\f078";

    color: #999;

    font-size: 14px;

    line-height: 1;

    -webkit-transition: color .3s ease-in 0s, transform .3s ease-in-out 0s;

    -webkit-transition: color .3s ease-in 0s, -webkit-transform .3s ease-in-out 0s;

    transition: color .3s ease-in 0s, -webkit-transform .3s ease-in-out 0s;

    transition: color .3s ease-in 0s, transform .3s ease-in-out 0s;

    transition: color .3s ease-in 0s, transform .3s ease-in-out 0s, -webkit-transform .3s ease-in-out 0s;

    -webkit-font-smoothing: antialiased;

    -moz-osx-font-smoothing: grayscale; }

.zhi-accordion.zhi-style2 .zhi-panel.zhi-active .zhi-panel-title:after {

  -webkit-transform: rotate(90deg);

          transform: rotate(90deg); }



/* ----- Style 1 ------ */

.zhi-accordion.zhi-style1 .zhi-panel {

  margin: 20px 0 0;

  background: #eee;

  border-radius: 5px;

  overflow: hidden; }

  .zhi-accordion.zhi-style1 .zhi-panel .zhi-panel-title {

    position: relative;

    display: block;

    padding: 10px 50px 10px 30px;

    font-size: 18px;

    line-height: 26px;

    letter-spacing: 0;

    font-weight: bold;

    color: #666;

    margin: 0; }

  .zhi-accordion.zhi-style1 .zhi-panel .zhi-panel-content {

    background: #f8f8f8;

    padding: 30px 30px; }

  .zhi-accordion.zhi-style1 .zhi-panel:hover .zhi-panel-title {

    background: #e2e2e2; }

  .zhi-accordion.zhi-style1 .zhi-panel.zhi-active .zhi-panel-title {

    color: #333;

    background: #e2e2e2; }

  .zhi-accordion.zhi-style1 .zhi-panel.zhi-active .zhi-panel-title:after {

    color: #333; }



/* ----- Style 2 ------ */

.zhi-accordion.zhi-style2 .zhi-panel {

  margin: 20px 0 0;

  color: #333;

  -webkit-transition: color .3s;

  transition: color .3s;

  position: relative; }

  .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title {

    position: relative;

    display: block;

    border-radius: 5px;

    border: 1px solid #dcdcdc;

    overflow: hidden;

    padding: 10px 50px 10px 30px;

    font-size: 18px;

    line-height: 26px;

    letter-spacing: 0;

    font-weight: bold;

    color: #666;

    margin: 0;

    -webkit-transition: all .3s ease-in-out 0s;

    transition: all .3s ease-in-out 0s;

    z-index: 1; }

    .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title:before {

      position: absolute;

      content: "";

      left: 0;

      bottom: 0;

      height: 0;

      width: 100%;

      background: #4c5053;

      -webkit-transition: height 0.3s cubic-bezier(0.77, 0, 0.175, 1);

      transition: height 0.3s cubic-bezier(0.77, 0, 0.175, 1);

      z-index: -1; }

  .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content {

    padding: 20px 30px;

    border-color: #dcdcdc;

    border-style: solid;

    border-width: 0 1px 1px 1px;

    border-radius: 0 0 5px 5px; }

  .zhi-accordion.zhi-style2 .zhi-panel:hover .zhi-panel-title, .zhi-accordion.zhi-style2 .zhi-panel:hover .zhi-panel-title:after {

    color: #fff;

    border-color: #333; }

  .zhi-accordion.zhi-style2 .zhi-panel:hover .zhi-panel-title:before {

    height: 100%; }

  .zhi-accordion.zhi-style2 .zhi-panel.zhi-active .zhi-panel-title {

    background: #4c5053;

    color: #fff;

    border-radius: 5px 5px 0 0;

    border-color: #333; }

  .zhi-accordion.zhi-style2 .zhi-panel.zhi-active .zhi-panel-title:after {

    color: #fff; }



.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title {

  color: #888;

  border-color: #404040; }

  .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-title:before {

    background: #e5e5e5; }

.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel:hover .zhi-panel-title {

  color: #333; }

.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel:hover .zhi-panel-title:after {

  color: #666; }

.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel.zhi-active .zhi-panel-title {

  background: #e5e5e5;

  color: #333;

  border-color: #e5e5e5; }

.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel.zhi-active .zhi-panel-title:after {

  color: #666; }

.zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content {

  border-color: #404040;

  color: #909090; }

  .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h1, .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h2, .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h3, .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h4, .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h5, .zhi-dark-bg .zhi-accordion.zhi-style2 .zhi-panel .zhi-panel-content h6 {

    color: #e5e5e5; }



/* ----- Style 3 ------ */

.zhi-accordion.zhi-style3 .zhi-panel {

  margin: 0;

  border-bottom: 1px solid #dcdcdc; }

  .zhi-accordion.zhi-style3 .zhi-panel:first-child {

    border-top: 1px solid #dcdcdc; }

  .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title {

    position: relative;

    display: block;

    padding: 10px 50px 10px 5px;

    font-size: 14px;

    line-height: 22px;

    letter-spacing: 1px;

    font-weight: bold;

    text-transform: uppercase;

    color: #666;

    margin: 0; }

    .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title:after {

      top: 14px; }

  .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content {

    padding: 10px 50px 30px 5px; }

  .zhi-accordion.zhi-style3 .zhi-panel:hover .zhi-panel-title {

    color: #333; }

  .zhi-accordion.zhi-style3 .zhi-panel.zhi-active .zhi-panel-title {

    color: #333; }

  .zhi-accordion.zhi-style3 .zhi-panel.zhi-active .zhi-panel-title:after {

    color: #333; }



.zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel {

  border-color: #404040; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title {

    color: #b0b0b0; }

    .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-title:after {

      color: #888; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel:hover .zhi-panel-title {

    color: #eaeaea; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel:hover .zhi-panel-title:after {

    color: #aaa; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel.zhi-active .zhi-panel-title {

    color: #eaeaea; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel.zhi-active .zhi-panel-title:after {

    color: #aaa; }

  .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content {

    color: #909090; }

    .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h1, .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h2, .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h3, .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h4, .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h5, .zhi-dark-bg .zhi-accordion.zhi-style3 .zhi-panel .zhi-panel-content h6 {

      color: #e5e5e5; }



