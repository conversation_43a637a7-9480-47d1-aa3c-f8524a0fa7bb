/**
 * CLS (Cumulative Layout Shift) 优化脚本
 * 用于减少页面布局偏移，提升用户体验
 */

(function() {
    'use strict';

    // 1. 图片懒加载优化
    function optimizeImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // 如果图片没有设置尺寸，尝试从父容器获取
            if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
                const parent = img.parentElement;
                if (parent) {
                    const computedStyle = window.getComputedStyle(parent);
                    const width = parseInt(computedStyle.width);
                    const height = parseInt(computedStyle.height);
                    
                    if (width && height) {
                        img.style.aspectRatio = `${width}/${height}`;
                    }
                }
            }
            
            // 添加加载状态
            img.addEventListener('load', function() {
                this.classList.add('loaded');
            });
            
            img.addEventListener('error', function() {
                this.classList.add('error');
                // 设置占位符
                this.style.backgroundColor = '#f0f0f0';
                this.style.minHeight = '200px';
            });
        });
    }

    // 2. 字体加载优化
    function optimizeFonts() {
        // 检测字体加载状态
        if ('fonts' in document) {
            document.fonts.ready.then(function() {
                document.body.classList.add('fonts-loaded');
            });
        }
        
        // 为关键字体添加预加载
        const criticalFonts = [
            'Microsoft Yahei',
            'FontAwesome'
        ];
        
        criticalFonts.forEach(font => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
            // 这里需要根据实际字体文件路径调整
            link.href = `./fonts/${font.replace(' ', '-').toLowerCase()}.woff2`;
            document.head.appendChild(link);
        });
    }

    // 3. 动态内容优化
    function optimizeDynamicContent() {
        // 为折叠内容预留空间
        const toggleContents = document.querySelectorAll('.vc_toggle_content');
        
        toggleContents.forEach(content => {
            const toggle = content.previousElementSibling;
            if (toggle && toggle.classList.contains('vc_toggle_title')) {
                toggle.addEventListener('click', function() {
                    // 在展开前预留空间
                    if (!content.classList.contains('active')) {
                        content.style.minHeight = '150px';
                        content.classList.add('expanding');
                        
                        // 展开完成后移除最小高度
                        setTimeout(() => {
                            content.style.minHeight = 'auto';
                            content.classList.remove('expanding');
                            content.classList.add('active');
                        }, 300);
                    }
                });
            }
        });
    }

    // 4. 广告位优化
    function optimizeAds() {
        const adContainers = document.querySelectorAll('.ad-container, [class*="ad-"], [id*="ad-"]');
        
        adContainers.forEach(container => {
            // 为广告位设置最小高度
            if (!container.style.minHeight) {
                container.style.minHeight = '250px';
                container.style.backgroundColor = '#f9f9f9';
                container.style.border = '1px dashed #ddd';
            }
            
            // 监听广告加载
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 广告内容已加载，移除占位样式
                        container.style.backgroundColor = '';
                        container.style.border = '';
                    }
                });
            });
            
            observer.observe(container, { childList: true, subtree: true });
        });
    }

    // 5. 导航栏优化
    function optimizeNavigation() {
        const header = document.getElementById('header');
        if (header) {
            // 确保导航栏有固定高度
            header.style.minHeight = '80px';
            
            // 监听滚动，优化固定导航栏
            let ticking = false;
            
            function updateHeader() {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                    header.style.minHeight = '60px';
                } else {
                    header.classList.remove('scrolled');
                    header.style.minHeight = '80px';
                }
                ticking = false;
            }
            
            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(updateHeader);
                    ticking = true;
                }
            });
        }
    }

    // 6. 表单优化
    function optimizeForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                // 确保表单元素有固定高度
                if (!input.style.minHeight) {
                    input.style.minHeight = '40px';
                }
                
                // 优化焦点状态
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });
    }

    // 7. 内容加载监控
    function monitorContentLoading() {
        // 监控新内容的插入
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 为新插入的图片设置尺寸
                            const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                            images.forEach(img => {
                                if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
                                    img.style.aspectRatio = '16/9'; // 默认宽高比
                                }
                            });
                            
                            // 为新内容添加加载动画
                            if (node.classList) {
                                node.classList.add('content-loading');
                                setTimeout(() => {
                                    node.classList.remove('content-loading');
                                    node.classList.add('content-loaded');
                                }, 100);
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 8. 性能监控
    function monitorCLS() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                        console.log('Layout shift detected:', entry.value);
                        
                        // 如果CLS值过高，记录相关信息
                        if (entry.value > 0.1) {
                            console.warn('High CLS detected:', {
                                value: entry.value,
                                sources: entry.sources,
                                startTime: entry.startTime
                            });
                        }
                    }
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    // 9. 初始化所有优化
    function initCLSOptimizations() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                runOptimizations();
            });
        } else {
            runOptimizations();
        }
    }
    
    function runOptimizations() {
        optimizeImages();
        optimizeFonts();
        optimizeDynamicContent();
        optimizeAds();
        optimizeNavigation();
        optimizeForms();
        monitorContentLoading();
        monitorCLS();
        
        // 标记优化完成
        document.body.classList.add('cls-optimized');
    }

    // 10. 工具函数
    const CLSUtils = {
        // 为元素预留空间
        reserveSpace: function(element, width, height) {
            if (element) {
                element.style.minWidth = width + 'px';
                element.style.minHeight = height + 'px';
            }
        },
        
        // 平滑显示元素
        smoothShow: function(element) {
            if (element) {
                element.style.opacity = '0';
                element.style.transition = 'opacity 0.3s ease';
                
                requestAnimationFrame(() => {
                    element.style.opacity = '1';
                });
            }
        },
        
        // 获取元素的稳定尺寸
        getStableSize: function(element) {
            const rect = element.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(element);
            
            return {
                width: rect.width || parseInt(computedStyle.width) || 0,
                height: rect.height || parseInt(computedStyle.height) || 0
            };
        }
    };

    // 暴露工具函数到全局
    window.CLSUtils = CLSUtils;

    // 启动优化
    initCLSOptimizations();

})();
