/**
 * 批量应用CLS优化脚本
 * 用于为所有HTML页面应用CLS优化
 */

const fs = require('fs');
const path = require('path');

// 需要优化的HTML文件列表
const htmlFiles = [
    'index.html',
    'download.html',
    'chajian.html',
    'about.html',
    'yonghu.html',
    'vsc.html',
    'faq.html'
];

// 需要添加的CSS和JS引用
const cssOptimization = `<!-- CLS优化样式 -->
<link rel="stylesheet" href="./cls-optimization.css" type="text/css" media="all">`;

const jsOptimization = `<!-- CLS优化脚本 -->
<script src="./cls-optimization.js" defer></script>`;

// 字体优化替换
const fontOptimization = {
    from: '<link rel="stylesheet" id="fontawesome-css" href="./index_files/font-awesome.min.css" type="text/css" media="all">',
    to: `<link rel="preload" href="./index_files/font-awesome.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="./index_files/font-awesome.min.css"></noscript>`
};

// CSS错误修复
const cssFixes = [
    {
        from: 'color: !important;',
        to: 'color: #000000!important;'
    },
    {
        from: 'color: }',
        to: 'color: #ffffff;}'
    },
    {
        from: 'color: ;',
        to: 'color: #ffffff;'
    }
];

function optimizeHtmlFile(filename) {
    try {
        console.log(`正在优化 ${filename}...`);
        
        // 读取文件内容
        let content = fs.readFileSync(filename, 'utf8');
        
        // 1. 添加CSS优化
        if (!content.includes('cls-optimization.css')) {
            content = content.replace(
                /<link rel="stylesheet" id="style-css" href="\.\/index_files\/style\.min\(1\)\.css" type="text\/css" media="all">/,
                `<link rel="stylesheet" id="style-css" href="./index_files/style.min(1).css" type="text/css" media="all">
${cssOptimization}`
            );
        }
        
        // 2. 添加JS优化
        if (!content.includes('cls-optimization.js')) {
            // 查找合适的位置插入JS
            const jsInsertPoints = [
                /<script charset="UTF-8" id="LA_COLLECT" src="\/\/sdk\.51\.la\/js-sdk-pro\.min\.js"><\/script>/,
                /<script>LA\.init\({id: "JhYo3WD0PkhHgSNJ",ck: "JhYo3WD0PkhHgSNJ",autoTrack:true}\)<\/script>/,
                /<script src="https:\/\/sdk\.51\.la\/perf\/js-sdk-perf\.min\.js" crossorigin="anonymous"><\/script>/
            ];
            
            for (let pattern of jsInsertPoints) {
                if (pattern.test(content)) {
                    content = content.replace(pattern, (match) => {
                        return match + '\n\n' + jsOptimization;
                    });
                    break;
                }
            }
        }
        
        // 3. 优化字体加载
        content = content.replace(fontOptimization.from, fontOptimization.to);
        
        // 4. 修复CSS错误
        cssFixes.forEach(fix => {
            content = content.replace(new RegExp(fix.from, 'g'), fix.to);
        });
        
        // 5. 为图片添加尺寸属性（如果没有的话）
        content = content.replace(
            /<img([^>]*src="[^"]*"[^>]*)>/g,
            (match, attrs) => {
                // 如果已经有width和height属性，跳过
                if (attrs.includes('width=') && attrs.includes('height=')) {
                    return match;
                }
                
                // 为常见图片添加默认尺寸
                if (attrs.includes('liebiao.png')) {
                    return `<img${attrs} width="716" height="757" style="aspect-ratio: 716/757;">`;
                } else if (attrs.includes('29018445_29018445_1477567217625.jpg')) {
                    return `<img${attrs} width="60" height="60" style="aspect-ratio: 1/1;">`;
                } else if (attrs.includes('20200908230057.png')) {
                    return `<img${attrs} width="1085" height="694" style="aspect-ratio: 1085/694;">`;
                }
                
                return match;
            }
        );
        
        // 6. 添加基本的CLS优化样式
        const basicCLSStyles = `
/* 基本CLS优化 */
img { max-width: 100%; height: auto; }
.vc_toggle_content { min-height: 100px; }
#header { min-height: 80px; }
.fa { font-display: swap; }
`;
        
        // 在</style>标签前添加基本样式
        content = content.replace(
            /(<\/style>)(?=\s*<link)/,
            basicCLSStyles + '$1'
        );
        
        // 写回文件
        fs.writeFileSync(filename, content, 'utf8');
        console.log(`✅ ${filename} 优化完成`);
        
    } catch (error) {
        console.error(`❌ 优化 ${filename} 时出错:`, error.message);
    }
}

function main() {
    console.log('开始批量应用CLS优化...\n');
    
    // 检查必要文件是否存在
    if (!fs.existsSync('cls-optimization.css')) {
        console.error('❌ cls-optimization.css 文件不存在');
        return;
    }
    
    if (!fs.existsSync('cls-optimization.js')) {
        console.error('❌ cls-optimization.js 文件不存在');
        return;
    }
    
    // 优化每个HTML文件
    htmlFiles.forEach(filename => {
        if (fs.existsSync(filename)) {
            optimizeHtmlFile(filename);
        } else {
            console.log(`⚠️  ${filename} 文件不存在，跳过`);
        }
    });
    
    console.log('\n🎉 批量优化完成！');
    console.log('\n建议测试步骤：');
    console.log('1. 打开 test-cls-optimization.html 进行测试');
    console.log('2. 使用 Chrome DevTools 检查 CLS 值');
    console.log('3. 运行 Lighthouse 性能测试');
    console.log('4. 验证结构化数据：https://search.google.com/test/rich-results');
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    optimizeHtmlFile,
    htmlFiles,
    cssOptimization,
    jsOptimization,
    fontOptimization,
    cssFixes
};
