/* CLS (Cumulative Layout Shift) 优化样式 */

/* 1. 图片优化 - 防止图片加载时的布局偏移 */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

img[width][height] {
    aspect-ratio: attr(width) / attr(height);
}

.vc_single_image-wrapper {
    position: relative;
    overflow: hidden;
}

.vc_single_image-img {
    width: 100%;
    height: auto;
    display: block;
}

/* 2. 字体加载优化 */
@font-face {
    font-family: 'Microsoft Yahei';
    font-display: swap;
}

.fa, [class*="fa-"] {
    font-display: swap;
}

/* 3. 为动态内容预留空间 */
.vc_toggle_content {
    min-height: 100px;
    transition: min-height 0.3s ease;
}

.vc_toggle_content.active {
    min-height: auto;
}

/* 4. 导航栏稳定性 */
#header {
    min-height: 80px;
}

#header.affix {
    min-height: 60px;
}

/* 5. 内容容器预留空间 */
.post, .zhi-portfolio {
    min-height: 200px;
}

.wpb_column {
    min-height: 50px;
}

/* 6. 按钮和链接稳定性 */
.btn {
    min-width: 120px;
    min-height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 7. 图标备用样式 - 防止字体图标加载时的偏移 */
.fa-eye-slash::before { content: "⚙"; }
.fa-star::before { content: "★"; }
.fa-pencil-square-o::before { content: "✎"; }
.fa-window-maximize::before { content: "⊞"; }
.fa-window-restore::before { content: "⊟"; }
.fa-code::before { content: "⌨"; }
.fa-columns::before { content: "⫸"; }
.fa-exchange::before { content: "⇄"; }
.fa-check::before { content: "✓"; }
.fa-chevron-down::before { content: "▼"; }
.fa-download::before { content: "⬇"; }
.fa-search::before { content: "🔍"; }

/* 8. 响应式优化 */
@media (max-width: 768px) {
    .vc_toggle_content {
        min-height: 80px;
    }
    
    .post, .zhi-portfolio {
        min-height: 150px;
    }
    
    #header {
        min-height: 60px;
    }
}

/* 9. 加载状态优化 */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 10. 防止内容跳跃 */
.content-container {
    contain: layout style;
}

/* 11. 优化表格和列表 */
table {
    table-layout: fixed;
    width: 100%;
}

ul, ol {
    min-height: 1.5em;
}

/* 12. 优化表单元素 */
input, textarea, select {
    min-height: 40px;
    box-sizing: border-box;
}

/* 13. 预加载关键资源的占位符 */
.preload-placeholder {
    background-color: #f5f5f5;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.preload-placeholder::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 14. 优化视频和iframe */
iframe, video {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
}

/* 15. 防止广告位布局偏移 */
.ad-container {
    min-height: 250px;
    background-color: #f9f9f9;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.ad-container::before {
    content: "广告位";
    color: #999;
    font-size: 14px;
}

/* 16. 优化搜索框 */
.search-container {
    min-height: 50px;
    position: relative;
}

/* 17. 优化分页 */
.pagination {
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 18. 优化侧边栏 */
.sidebar, .widget-area {
    min-height: 300px;
}

/* 19. 优化面包屑导航 */
.breadcrumb {
    min-height: 30px;
    display: flex;
    align-items: center;
}

/* 20. 优化页脚 */
#footer {
    min-height: 200px;
}

/* 21. 关键CSS内联优化 */
.critical-content {
    visibility: visible;
    opacity: 1;
}

.non-critical-content {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.non-critical-content.loaded {
    visibility: visible;
    opacity: 1;
}

/* 22. 防止FOUC (Flash of Unstyled Content) */
.no-js .js-only {
    display: none;
}

.js .no-js-only {
    display: none;
}

/* 23. 优化滚动性能 */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* 24. 容器查询支持 */
@container (min-width: 768px) {
    .container-responsive {
        min-height: 400px;
    }
}

/* 25. 减少重绘和重排 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 26. 优化动画性能 */
.animate-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 27. 预留空间的工具类 */
.reserve-space-sm { min-height: 50px; }
.reserve-space-md { min-height: 100px; }
.reserve-space-lg { min-height: 200px; }
.reserve-space-xl { min-height: 300px; }

/* 28. 内容稳定性 */
.stable-content {
    contain: layout style paint;
}

/* 29. 防止内容溢出 */
.prevent-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 30. 优化加载状态 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
