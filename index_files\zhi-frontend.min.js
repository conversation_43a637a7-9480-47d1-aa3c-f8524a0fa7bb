if(typeof jQuery!="undefined"){jQuery.noConflict();(function($){"use strict";$(function(){var LVCA_Tabs_Mgr={init:function(){var self=this;if($(".zhi-tabs").length===0)return;self.initHash();self.initAnchor();$(window).on("hashchange.lvca.tabs",function(){self.initHash()})},initAnchor:function(){var self=this;$('a[href*="#"]').not(".zhi-tab-label").click(function(event){var hash=$(this).attr("href").split("#").pop();if(""!==hash){var $element=$("#"+hash);if($element.length>0){if($element.hasClass("zhi-tab-pane")){event.preventDefault();self.displayTab($element)}}}})},initHash:function(){var self=this;var hash=window.location.hash.replace("#","").split("/").shift();if(""!==hash){var $element=$("#"+hash);if($element.length>0){if($element.hasClass("zhi-tab-pane")){setTimeout(function(){self.displayTab($element)},100)}}}},displayTab:function($tabPane){var index,offset,speed,$tabs,$mobileMenu;offset=.2;speed=300;$tabs=$tabPane.closest(".zhi-tabs");$mobileMenu=$tabs.find(".zhi-tab-mobile-menu");$mobileMenu.trigger("click");index=$tabs.find(".zhi-tab-pane").index($tabPane);var $tabNav=$tabs.find(".zhi-tab-nav > .zhi-tab").eq(index);$tabNav.trigger("click");$("html, body").animate({scrollTop:Math.round($tabs.offset().top-$(window).height()*offset)},speed)}};var LVCA_Accordion_Mgr={init:function(){var self=this;if($(".zhi-accordion").length===0)return;self.initHash();self.initAnchor();jQuery(window).on("hashchange.lvca.accordion",function(){self.initHash()})},initAnchor:function(){var self=this;jQuery('a[href*="#"]').click(function(event){var hash=jQuery(this).attr("href").split("#").pop();if(""!==hash){var $element=jQuery("#"+hash);if($element.length>0){if($element.hasClass("zhi-panel")){event.preventDefault();self.displayPanel($element)}}}})},initHash:function(){var self=this;var hash,$element;hash=window.location.hash.replace("#","").split("/").shift();if(""!==hash){$element=jQuery("#"+hash);if($element.length>0){if($element.hasClass("zhi-panel")){setTimeout(function(){self.displayPanel($element)},100)}}}},displayPanel:function($panel){var self=this;var offset,speed;offset=.2;speed=300;if(!$panel.hasClass("zhi-active")){var $panelLabel=$panel.find(".zhi-panel-title").eq(0);$panelLabel.trigger("click")}setTimeout(function(){jQuery("html, body").animate({scrollTop:$panel.offset().top-jQuery(window).height()*offset},speed)},300)}};LVCA_Tabs_Mgr.init();LVCA_Accordion_Mgr.init()})})(jQuery)}