.empty-wpmenucart {

	display: none !important;

}



.cartcontents + .amount:before {

	content: '-';

	margin: 0 0.25em;

}



.wpmenucart-display-standard {

	

}

.wpmenucart-display-right {

	float: right !important;

}

.wpmenucart-display-left {

	float: left !important;

}

.wpmenucart-icon-shopping-cart {

	background-image: none;

	vertical-align: inherit;

}



.wpmenucart-thumbnail img {

	width: 32px;

	height: auto;	

	margin-right: 8px;

	box-shadow: 0 1px 2px 0 rgba(0,0,0,0.3);

	-webkit-box-shadow: 0 1px 2px 0 rgba(0,0,0,0.3);

	-moz-box-shadow: 0 1px 2px 0 rgba(0,0,0,0.3);

}



.wpmenucart-submenu-item a {

	overflow: hidden;

    white-space:nowrap !important;

}



.wpmenucart-thumbnail, .wpmenucart-order-item-info {

   display:inline-block;

   vertical-align:middle;

}



.wpmenucart-product-name, .wpmenucart-product-quantity-price {

	display:block;

}



.wpmenucart .clearfix:after {content: "."; display: block; height: 0; clear: both; visibility: hidden;}

.wpmenucart .clearfix {display: inline-block;}

/* Hides from IE-mac \*/

* html .wpmenucart .clearfix {height: 1%;}

.wpmenucart .clearfix {display: block;}

/* End hide from IE-mac */

/* Force Inline Display */

li.wpmenucartli a.wpmenucart-contents span{

	display: inline-block !important;

}

