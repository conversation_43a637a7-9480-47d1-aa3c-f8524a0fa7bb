<?php
$menu = '{
     "button":[
     {  
          "type":"view",
          "name":"资源目录",
          "url":"https://mp.weixin.qq.com/s?__biz=Mzg5NTc1OTc4Nw==&mid=2247483753&idx=1&sn=b8da7aab5594472cc82d8cc1eaf31d02&chksm=c00a25adf77dacbb089c12e7ea8d201c4c1fe5b4a35fc994b420280835d833507a67d21b2795&token=1783814067&lang=zh_CN#rd"
      },
      {  
          "type":"view",
          "name":"加入会员",
          "url":"https://mp.weixin.qq.com/s?__biz=Mzg5NTc1OTc4Nw==&mid=2247483753&idx=1&sn=b8da7aab5594472cc82d8cc1eaf31d02&chksm=c00a25adf77dacbb089c12e7ea8d201c4c1fe5b4a35fc994b420280835d833507a67d21b2795&token=1783814067&lang=zh_CN#rd"
      }
      ]
}';
createMenu($data);
function createMenu($data)
{
    $access_token = getAccesstoken();
    $url  = 'https://api.weixin.qq.com/cgi-bin/menu/create?access_token=' . $access_token;
    $info = json_decode(postjson($url, $data)['1'], true);
    var_dump($info);
}
function getAccesstoken()
{
    $appid  = 'wxe705a2c41de14df5';
    $secret = '0c8e9796a5404384deee35a99a7d05e4';
    $url    = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";
    $res = get($url);
    $res = json_decode($res, true);
    if (!empty($res['errcode'])) {
        return $res['errmsg'];
    }
    return $res['access_token'];
}
function get($url, $header = [], $timeOut = 30, $noback = 0, &$httpCode = null)
{
    $timeOut = empty($timeOut) ? 0 : (int) $timeOut;
    $header  = empty($header) ? array() : (array) $header;
    $curl    = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 5.1; .' .
        'zh-CN; rv:1.9.2.8) Gecko/20100722 Firefox/3.6.8');
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeOut);
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeOut);
    if ($noback) {
        curl_setopt($curl, CURLOPT_NOBODY, 1);
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    if ($header) {
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    }
    $str      = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    return $str;
}
function postjson($url, $jsonStr, $timeOut = 30)
{
    $timeOut = empty($timeOut) ? 0 : (int) $timeOut;
    $ch      = curl_init();
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeOut);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeOut);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json; charset=utf-8',
        'Content-Length: ' . strlen($jsonStr),
    )
    );
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return array($httpCode, $response);
}