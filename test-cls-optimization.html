<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CLS优化测试页面</title>
    <link rel="stylesheet" href="./cls-optimization.css">
    <style>
        body {
            font-family: "Microsoft Yahei", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #00c1cf;
            padding-bottom: 10px;
        }
        
        .cls-monitor {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
        }
        
        .test-image {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        
        .test-button {
            background: #00c1cf;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #009aa6;
        }
        
        .dynamic-content {
            min-height: 100px;
            background: #f9f9f9;
            border: 1px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
        }
        
        .loading {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .test-results {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .test-warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .test-error {
            background: #f8d7da;
            border: 1px solid #dc3545;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="cls-monitor" id="clsMonitor">
        <div>CLS值: <span id="clsValue">0</span></div>
        <div>布局偏移次数: <span id="shiftCount">0</span></div>
    </div>

    <div class="test-container">
        <h1>CLS优化测试页面</h1>
        <p>此页面用于测试CLS优化效果。请观察右上角的CLS监控器，正常情况下CLS值应该保持在0.1以下。</p>

        <!-- 测试1: 图片优化 -->
        <div class="test-section">
            <h2>测试1: 图片优化</h2>
            <p>测试图片是否正确设置了尺寸属性，避免加载时的布局偏移。</p>
            
            <img src="./index_files/20200908230057.png" 
                 width="400" 
                 height="256" 
                 alt="测试图片" 
                 class="test-image"
                 style="aspect-ratio: 400/256;">
            
            <button class="test-button" onclick="testImageLoading()">测试动态加载图片</button>
            <div id="imageTestResult"></div>
        </div>

        <!-- 测试2: 字体优化 -->
        <div class="test-section">
            <h2>测试2: 字体优化</h2>
            <p>测试字体图标是否有备用字符，避免字体加载时的布局偏移。</p>
            
            <div style="font-size: 24px; margin: 10px 0;">
                <i class="fa fa-star"></i> 星标图标
                <i class="fa fa-download"></i> 下载图标
                <i class="fa fa-check"></i> 检查图标
            </div>
            
            <button class="test-button" onclick="testFontLoading()">测试字体加载</button>
            <div id="fontTestResult"></div>
        </div>

        <!-- 测试3: 动态内容 -->
        <div class="test-section">
            <h2>测试3: 动态内容优化</h2>
            <p>测试动态插入内容时是否预留了空间。</p>
            
            <button class="test-button" onclick="testDynamicContent()">插入动态内容</button>
            <button class="test-button" onclick="clearDynamicContent()">清除内容</button>
            
            <div id="dynamicContainer" class="dynamic-content">
                动态内容将在这里显示...
            </div>
        </div>

        <!-- 测试4: 折叠内容 -->
        <div class="test-section">
            <h2>测试4: 折叠内容优化</h2>
            <p>测试折叠内容展开时是否有平滑的过渡。</p>
            
            <div class="vc_toggle_title" onclick="toggleContent()" style="cursor: pointer; background: #f0f0f0; padding: 10px; border-radius: 5px;">
                <h4>点击展开/收起内容 <i class="fa fa-chevron-down"></i></h4>
            </div>
            <div class="vc_toggle_content" id="toggleContent" style="display: none; padding: 20px; background: #f9f9f9;">
                <p>这是折叠的内容。展开时应该有平滑的动画效果，不会造成布局偏移。</p>
                <p>内容可以很长，包含多个段落和元素。</p>
                <ul>
                    <li>列表项目1</li>
                    <li>列表项目2</li>
                    <li>列表项目3</li>
                </ul>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <h2>测试结果</h2>
            <div id="testResults">
                <p>请执行上述测试，结果将在这里显示。</p>
            </div>
        </div>
    </div>

    <script src="./cls-optimization.js"></script>
    <script>
        // CLS监控
        let clsValue = 0;
        let shiftCount = 0;

        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                        clsValue += entry.value;
                        shiftCount++;
                        
                        document.getElementById('clsValue').textContent = clsValue.toFixed(4);
                        document.getElementById('shiftCount').textContent = shiftCount;
                        
                        // 更新监控器颜色
                        const monitor = document.getElementById('clsMonitor');
                        if (clsValue > 0.25) {
                            monitor.style.background = '#dc3545'; // 红色
                        } else if (clsValue > 0.1) {
                            monitor.style.background = '#ffc107'; // 黄色
                        } else {
                            monitor.style.background = '#28a745'; // 绿色
                        }
                    }
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }

        // 测试函数
        function testImageLoading() {
            const container = document.getElementById('imageTestResult');
            container.innerHTML = '<div class="loading" style="height: 50px; margin: 10px 0;"></div>';
            
            setTimeout(() => {
                const img = document.createElement('img');
                img.src = './index_files/20200908230057.png';
                img.width = 200;
                img.height = 128;
                img.alt = '动态加载的图片';
                img.style.aspectRatio = '200/128';
                
                container.innerHTML = '<div class="test-results">✅ 图片已加载，检查CLS值变化</div>';
                container.appendChild(img);
            }, 1000);
        }

        function testFontLoading() {
            const container = document.getElementById('fontTestResult');
            container.innerHTML = '<div class="test-results">✅ 字体图标测试完成，备用字符应该可见</div>';
        }

        function testDynamicContent() {
            const container = document.getElementById('dynamicContainer');
            container.classList.add('loading');
            
            setTimeout(() => {
                container.classList.remove('loading');
                container.innerHTML = `
                    <h3>动态插入的内容</h3>
                    <p>这是通过JavaScript动态插入的内容。如果优化正确，应该不会造成明显的布局偏移。</p>
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 5px;">
                        <strong>提示：</strong> 观察右上角的CLS监控器，值应该保持稳定。
                    </div>
                `;
            }, 800);
        }

        function clearDynamicContent() {
            const container = document.getElementById('dynamicContainer');
            container.innerHTML = '动态内容将在这里显示...';
        }

        function toggleContent() {
            const content = document.getElementById('toggleContent');
            const isVisible = content.style.display !== 'none';
            
            if (isVisible) {
                content.style.display = 'none';
            } else {
                content.style.display = 'block';
                // 使用CLS优化工具
                if (window.CLSUtils) {
                    CLSUtils.smoothShow(content);
                }
            }
        }

        // 页面加载完成后的测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                const results = document.getElementById('testResults');
                const finalCLS = clsValue;
                
                let resultHTML = '';
                
                if (finalCLS <= 0.1) {
                    resultHTML = `<div class="test-results">
                        <h3>✅ CLS优化测试通过</h3>
                        <p>最终CLS值: ${finalCLS.toFixed(4)} (良好)</p>
                        <p>布局偏移次数: ${shiftCount}</p>
                    </div>`;
                } else if (finalCLS <= 0.25) {
                    resultHTML = `<div class="test-warning">
                        <h3>⚠️ CLS优化需要改进</h3>
                        <p>最终CLS值: ${finalCLS.toFixed(4)} (需要改进)</p>
                        <p>布局偏移次数: ${shiftCount}</p>
                    </div>`;
                } else {
                    resultHTML = `<div class="test-error">
                        <h3>❌ CLS优化效果不佳</h3>
                        <p>最终CLS值: ${finalCLS.toFixed(4)} (较差)</p>
                        <p>布局偏移次数: ${shiftCount}</p>
                    </div>`;
                }
                
                results.innerHTML = resultHTML;
            }, 3000);
        });
    </script>
</body>
</html>
